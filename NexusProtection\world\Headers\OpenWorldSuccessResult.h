/*
 * OpenWorldSuccessResult.h - World Opening Success Handler
 * Refactored for Visual Studio 2022 compatibility
 * Original: pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <chrono>

// Forward declarations
class CMainThread;
class CLogFile;
class CWnd;

/**
 * World Opening Success Result Handler
 * Handles successful world opening operations and database initialization
 */
class OpenWorldSuccessResultHandler {
public:
    // Constructor/Destructor
    OpenWorldSuccessResultHandler();
    virtual ~OpenWorldSuccessResultHandler();

    // Main world opening success processing
    static bool ProcessOpenWorldSuccessResult(CMainThread* pMainThread, 
                                            uint8_t byWorldCode, 
                                            const char* pszDBName, 
                                            const char* pszDBIP);
    
    // Validation functions
    static bool ValidateMainThreadInstance(const CMainThread* pMainThread);
    static bool ValidateWorldCode(uint8_t byWorldCode);
    static bool ValidateDatabaseParameters(const char* pszDBName, const char* pszDBIP);
    
    // Database operations
    static bool InitializeDatabase(CMainThread* pMainThread, const char* pszDBName, const char* pszDBIP);
    static bool ValidateDatabaseConnection(const char* pszDBName, const char* pszDBIP);
    
    // World management functions
    static void SetWorldCode(CMainThread* pMainThread, uint8_t byWorldCode);
    static void SetWorldOpenState(CMainThread* pMainThread, bool bOpen);
    static bool GetWorldOpenState(const CMainThread* pMainThread);
    
    // Service management
    static void StartSelfService(CMainThread* pMainThread);
    static bool IsServiceRunning(const CMainThread* pMainThread);
    
    // Logging and history functions
    static void WriteServerStartHistory(const char* format, ...);
    static void LogSystemError(CMainThread* pMainThread, const char* errorMessage);
    static void LogWorldOpenSuccess(uint8_t byWorldCode, const char* pszDBName, const char* pszDBIP);
    static void LogDatabaseInitResult(bool bSuccess, const char* pszDBName, const char* pszDBIP);
    
    // Error handling
    static void HandleDatabaseInitFailure(CMainThread* pMainThread);
    static void HandleWorldOpenError(const char* errorMessage, const CMainThread* pMainThread = nullptr);
    static void SendErrorMessage(CWnd* pFrame, uint32_t message);

private:
    // Internal processing helpers
    static void InitializeProcessingContext();
    static void CleanupProcessingContext();
    static std::string GetCurrentDateString();
    static std::string GetCurrentTimeString();
    static void TraceWorldOpen(const std::string& dateStr, const std::string& timeStr);
    
    // Security and validation
    static bool ValidateSecurityCookie();
    static void InitializeStackSecurity();
    
    // Constants
    static constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    static constexpr size_t STACK_INIT_SIZE = 92 * sizeof(uint32_t);
    static constexpr uint32_t WM_CLOSE_MESSAGE = 0x10;
    
    // Disable copy constructor and assignment operator
    OpenWorldSuccessResultHandler(const OpenWorldSuccessResultHandler&) = delete;
    OpenWorldSuccessResultHandler& operator=(const OpenWorldSuccessResultHandler&) = delete;
};

/**
 * Legacy C-style interface for compatibility
 * These functions maintain the original calling convention for existing code
 */
namespace OpenWorldSuccessResultLegacy {
    // Original function signature for compatibility
    void pc_OpenWorldSuccessResult(CMainThread* pThis, char byWorldCode, char* pszDBName, char* pszDBIP);
    
    // Helper functions for legacy integration
    void InitializeLegacySupport();
    void CleanupLegacySupport();
}

/**
 * Utility functions for world opening management
 */
namespace OpenWorldSuccessResultUtils {
    // Parameter validation
    bool IsValidWorldCode(uint8_t byWorldCode);
    bool IsValidDatabaseName(const char* pszDBName);
    bool IsValidDatabaseIP(const char* pszDBIP);
    
    // String utilities
    std::string SafeStringCopy(const char* source, size_t maxLength = 256);
    std::string FormatDatabaseInfo(const char* pszDBName, const char* pszDBIP);
    
    // Time utilities
    std::string GetFormattedDate();
    std::string GetFormattedTime();
    std::chrono::system_clock::time_point GetCurrentTimestamp();
    
    // Logging utilities
    void LogWorldOpenCall(const char* functionName, uint8_t byWorldCode, 
                         const char* pszDBName, const char* pszDBIP);
    void LogError(const char* errorMessage, const char* context = nullptr);
    void LogDatabaseOperation(const char* operation, const char* dbName, const char* dbIP, bool success);
}

/**
 * Database initialization result structure
 */
struct DatabaseInitResult {
    bool success;
    std::string errorMessage;
    std::string dbName;
    std::string dbIP;
    std::chrono::system_clock::time_point timestamp;
    
    DatabaseInitResult() : success(false), timestamp(std::chrono::system_clock::now()) {}
};

/**
 * World opening context structure
 */
struct WorldOpenContext {
    uint8_t worldCode;
    std::string dbName;
    std::string dbIP;
    std::chrono::system_clock::time_point startTime;
    bool isInitialized;
    
    WorldOpenContext() : worldCode(0), startTime(std::chrono::system_clock::now()), isInitialized(false) {}
};

// Type definitions for better code clarity
using WorldOpenHandler = bool(*)(CMainThread*, uint8_t, const char*, const char*);
using DatabaseInitHandler = bool(*)(CMainThread*, const char*, const char*);
using ErrorCallback = void(*)(const char*, const CMainThread*);

// Constants for world opening processing
namespace OpenWorldSuccessResultConstants {
    constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    constexpr size_t STACK_INIT_SIZE = 92 * sizeof(uint32_t);
    constexpr uint32_t WM_CLOSE_MESSAGE = 0x10;
    constexpr uint8_t INVALID_WORLD_CODE = 0xFF;
    constexpr size_t MAX_DB_NAME_LENGTH = 128;
    constexpr size_t MAX_DB_IP_LENGTH = 64;
    constexpr size_t DATE_BUFFER_SIZE = 16;
    constexpr size_t TIME_BUFFER_SIZE = 16;
    
    // Database operation types
    constexpr const char* DB_INIT_BEGIN = "DBInit Begin";
    constexpr const char* DB_INIT_COMPLETE = "DBInit Complete";
    constexpr const char* DB_INIT_FAIL = "DBInit Fail";
    
    // Log message formats
    constexpr const char* LOG_FORMAT_DB_INIT = "DBInit Begin >> name: %s, ip: %s";
    constexpr const char* LOG_FORMAT_TRACE = "%s-%s: Open World";
}
