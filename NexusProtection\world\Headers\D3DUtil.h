#pragma once

#include <cstdint>

// Forward declarations for DirectX types
struct _DDSURFACEDESC2;

struct D3DXMATRIX {
    float m[4][4];
    
    // Default constructor
    D3DXMATRIX() = default;
    
    // Constructor with values
    D3DXMATRIX(float m00, float m01, float m02, float m03,
               float m10, float m11, float m12, float m13,
               float m20, float m21, float m22, float m23,
               float m30, float m31, float m32, float m33);
    
    // Copy constructor and assignment
    D3DXMATRIX(const D3DXMATRIX& other) = default;
    D3DXMATRIX& operator=(const D3DXMATRIX& other) = default;
    
    // Access operators
    float* operator[](int row) { return m[row]; }
    const float* operator[](int row) const { return m[row]; }
};

// Vector3 structure for 3D positions and directions
struct D3DXVECTOR3 {
    float x, y, z;
    
    D3DXVECTOR3() : x(0.0f), y(0.0f), z(0.0f) {}
    D3DXVECTOR3(float x_, float y_, float z_) : x(x_), y(y_), z(z_) {}
    
    // Copy constructor and assignment
    D3DXVECTOR3(const D3DXVECTOR3& other) = default;
    D3DXVECTOR3& operator=(const D3DXVECTOR3& other) = default;
};

/**
 * @namespace D3DUtil
 * @brief DirectX utility functions for matrix and vector operations
 * 
 * This namespace contains utility functions for DirectX matrix operations,
 * particularly for cube map view matrix generation and related transformations.
 * 
 * @note Refactored from decompiled D3D utility functions
 */
namespace D3DUtil {

    // Constants for cube map face indices
    enum class CubeMapFace : uint32_t {
        POSITIVE_X = 0,  // Right face
        NEGATIVE_X = 1,  // Left face
        POSITIVE_Y = 2,  // Top face
        NEGATIVE_Y = 3,  // Bottom face
        POSITIVE_Z = 4,  // Front face
        NEGATIVE_Z = 5   // Back face
    };

    // Common matrix constants
    namespace Constants {
        constexpr float FLOAT_1_0 = 1.0f;
        constexpr float FLOAT_N1_0 = -1.0f;
        constexpr float FLOAT_0_0 = 0.0f;
    }

    /**
     * @brief Generates a view matrix for cube map rendering
     * @param faceIndex The cube map face index (0-5)
     * @return D3DXMATRIX The generated view matrix for the specified face
     * 
     * This function creates a view matrix suitable for rendering to a specific
     * face of a cube map. Each face has a predefined orientation:
     * - Face 0 (POSITIVE_X): Right face, looking towards +X
     * - Face 1 (NEGATIVE_X): Left face, looking towards -X  
     * - Face 2 (POSITIVE_Y): Top face, looking towards +Y
     * - Face 3 (NEGATIVE_Y): Bottom face, looking towards -Y
     * - Face 4 (POSITIVE_Z): Front face, looking towards +Z
     * - Face 5 (NEGATIVE_Z): Back face, looking towards -Z
     * 
     * @note Original function: D3DUtil_GetCubeMapViewMatrix
     */
    D3DXMATRIX GetCubeMapViewMatrix(uint32_t faceIndex);

    /**
     * @brief Generates a view matrix for cube map rendering (enum version)
     * @param face The cube map face enum value
     * @return D3DXMATRIX The generated view matrix for the specified face
     */
    D3DXMATRIX GetCubeMapViewMatrix(CubeMapFace face);

    /**
     * @brief Creates a left-handed look-at matrix
     * @param eye The eye position
     * @param at The target position to look at
     * @param up The up vector
     * @return D3DXMATRIX The resulting look-at matrix
     * 
     * This function creates a left-handed coordinate system look-at matrix.
     * It's used internally by GetCubeMapViewMatrix.
     */
    D3DXMATRIX MatrixLookAtLH(const D3DXVECTOR3& eye, 
                              const D3DXVECTOR3& at, 
                              const D3DXVECTOR3& up);

    /**
     * @brief Validates a cube map face index
     * @param faceIndex The face index to validate
     * @return true if the face index is valid (0-5)
     */
    bool IsValidCubeMapFace(uint32_t faceIndex);

    /**
     * @brief Converts cube map face enum to index
     * @param face The cube map face enum
     * @return uint32_t The corresponding face index
     */
    uint32_t CubeMapFaceToIndex(CubeMapFace face);

    /**
     * @brief Converts cube map face index to enum
     * @param faceIndex The face index
     * @return CubeMapFace The corresponding face enum
     */
    CubeMapFace IndexToCubeMapFace(uint32_t faceIndex);

    /**
     * @brief Gets the name of a cube map face for debugging
     * @param face The cube map face
     * @return const char* The name of the face
     */
    const char* GetCubeMapFaceName(CubeMapFace face);

    /**
     * @brief Calculates the size to skip for mip map levels
     * @param surfaceDesc Pointer to DirectX surface description (modified in-place)
     * @param targetMipLevel Target mip map level to reach
     * @param maxWidth Maximum width constraint
     * @param maxHeight Maximum height constraint
     * @return uint32_t Total size of skipped mip map data in bytes
     *
     * This function calculates how much data to skip when loading mip maps
     * to reach a specific mip level or size constraint. It modifies the
     * surface description to reflect the final mip level parameters.
     *
     * @note Original function: GetMipMapSkipSize (Address: 0x1404FFE70)
     */
    uint32_t GetMipMapSkipSize(struct _DDSURFACEDESC2* surfaceDesc,
                               uint32_t targetMipLevel,
                               uint32_t maxWidth,
                               uint32_t maxHeight);

    /**
     * @brief Validates mip map parameters
     * @param width Surface width
     * @param height Surface height
     * @return bool true if the parameters are valid for mip mapping
     */
    bool ValidateMipMapParameters(uint32_t width, uint32_t height);

    /**
     * @brief Calculates the number of mip map levels for given dimensions
     * @param width Surface width
     * @param height Surface height
     * @return uint32_t Number of possible mip map levels
     */
    uint32_t CalculateMipMapLevels(uint32_t width, uint32_t height);

} // namespace D3DUtil

// Legacy C-style interface for compatibility
extern "C" {
    /**
     * @brief Legacy C interface for GetCubeMapViewMatrix
     * @param retstr Pointer to the result matrix
     * @param faceIndex The cube map face index
     * @return D3DXMATRIX* Pointer to the result matrix
     */
    D3DXMATRIX* D3DUtil_GetCubeMapViewMatrix(D3DXMATRIX* retstr, int faceIndex);
    
    /**
     * @brief Legacy C interface for MatrixLookAtLH
     * @param result Pointer to the result matrix
     * @param eye Pointer to the eye position
     * @param at Pointer to the target position
     * @param up Pointer to the up vector
     * @return D3DXMATRIX* Pointer to the result matrix
     */
    D3DXMATRIX* D3DXMatrixLookAtLH_0(D3DXMATRIX* result,
                                     const int* eye,
                                     const char* at,
                                     const float* up);

    /**
     * @brief Legacy C interface for GetMipMapSkipSize
     * @param surfaceDesc Pointer to DirectX surface description
     * @param targetMipLevel Target mip map level
     * @param maxWidth Maximum width constraint
     * @param maxHeight Maximum height constraint
     * @return int64_t Total size of skipped mip map data
     */
    int64_t GetMipMapSkipSize(struct _DDSURFACEDESC2* surfaceDesc,
                              unsigned int targetMipLevel,
                              unsigned int maxWidth,
                              unsigned int maxHeight);
}
