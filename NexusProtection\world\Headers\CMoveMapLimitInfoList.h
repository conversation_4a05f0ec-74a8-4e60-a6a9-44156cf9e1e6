#pragma once

/**
 * @file CMoveMapLimitInfoList.h
 * @brief Map movement limitation information list management
 * @details Container class that manages a list of map movement limitation information objects
 * <AUTHOR> Development Team
 * @date 2024
 */

#include <vector>
#include <memory>

// Forward declarations
class CMoveMapLimitInfo;
class CMoveMapLimitRightInfo;
class CPlayer;

/**
 * @class CMoveMapLimitInfoList
 * @brief Container for managing map movement limitation information
 * 
 * This class manages a collection of CMoveMapLimitInfo objects that define
 * movement restrictions and limitations for different maps and scenarios.
 * It provides functionality to:
 * - Store and manage limitation information objects
 * - Load player-specific limitation data
 * - Process movement limitation requests
 * - Find specific limitation information by criteria
 */
class CMoveMapLimitInfoList
{
public:
    // Constructor and destructor
    CMoveMapLimitInfoList();
    ~CMoveMapLimitInfoList();

    // Core functionality
    bool Init(const std::vector<int>& vecRightTypeList);
    void Load(CPlayer* pkPlayer, CMoveMapLimitRightInfo* pkRight);
    char Request(int iLimitType, int iRequestType, int iMapIndex,
                unsigned int dwStoreRecordIndex, int iUserIndex, char* pRequest,
                CMoveMapLimitRightInfo* pkRight);

    // Information retrieval
    CMoveMapLimitInfo* Get(int iLimitType, int iMapIndex, unsigned int dwStoreRecordIndex);

    // Utility methods
    void CleanUp();
    size_t Size() const;
    bool Empty() const;

private:
    // Prevent copying
    CMoveMapLimitInfoList(const CMoveMapLimitInfoList&) = delete;
    CMoveMapLimitInfoList& operator=(const CMoveMapLimitInfoList&) = delete;

    // Helper methods
    void ClearLimitInfo();
    bool CreateLimitInfo(int iLimitType, int iMapIndex);

private:
    // Member variables
    std::vector<CMoveMapLimitInfo*> m_vecLimitInfo;  ///< Vector of limitation information objects
};
