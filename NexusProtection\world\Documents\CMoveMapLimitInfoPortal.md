# CMoveMapLimitInfoPortal

## Overview

The `CMoveMapLimitInfoPortal` class is a derived class of `CMoveMapLimitInfo` that implements portal-specific map movement limitations within the NexusProtection game system. It provides specialized functionality for managing portal-based movement restrictions and related operations.

## Purpose

This derived class serves as the concrete implementation for:
- **Portal Movement Control**: Managing movement through specific portals
- **Dummy Object Management**: Handling source, destination, and regeneration dummy objects
- **Allowed Code Validation**: Maintaining and checking allowed dummy codes
- **Force Move HQ Operations**: Processing headquarters force movement requests
- **Timer-Based Notifications**: Managing time-based notification systems
- **Configuration Loading**: Loading portal-specific settings from INI files

## Architecture

### Inheritance Hierarchy
```
CMoveMapLimitInfo (Abstract Base)
    └── CMoveMapLimitInfoPortal (Concrete Implementation)
```

### Key Components
- **Dummy Objects**: Source, destination, and regeneration dummy management
- **Allowed Codes**: Vector-based storage for permitted dummy codes
- **Notification System**: State-based force move HQ notifications with timer support
- **Request Processing**: Specialized handlers for different portal request types

## Class Interface

### Core Virtual Methods (Overridden)
- `Request(...)`: Main request processing method with portal-specific logic

### Portal-Specific Methods
- `LoadINI()`: Load configuration from INI files
- `ProcForceMoveHQ(pRequest)`: Process force move headquarters requests
- `ProcGotoLimitZone(pRequest)`: Process goto limit zone requests
- `ProcUseMoveScroll(pRequest)`: Process move scroll usage requests

### Dummy Management
- `SetSrcDummy(pDummy)`, `GetSrcDummy()`: Source dummy object management
- `SetDestDummy(pDummy)`, `GetDestDummy()`: Destination dummy object management
- `SetRegenDummy(pDummy)`, `GetRegenDummy()`: Regeneration dummy object management

### Allowed Code Management
- `AddAllowedDummyCode(code)`: Add permitted dummy code
- `ClearAllowedDummyCodes()`: Remove all allowed codes
- `IsAllowedDummyCode(code)`: Check if code is permitted
- `GetAllowedDummyCodeCount()`: Get number of allowed codes

## Usage Example

```cpp
// Create a portal limitation info object
CMoveMapLimitInfoPortal* pPortalInfo = new CMoveMapLimitInfoPortal(portalIndex, 
                                                                  static_cast<int>(LimitType::Portal));

if (pPortalInfo)
{
    // Load configuration
    if (pPortalInfo->LoadINI())
    {
        // Set up dummy objects
        pPortalInfo->SetSrcDummy(pSourceDummy);
        pPortalInfo->SetDestDummy(pDestDummy);
        pPortalInfo->SetRegenDummy(pRegenDummy);
        
        // Add allowed dummy codes
        pPortalInfo->AddAllowedDummyCode("PORTAL_001");
        pPortalInfo->AddAllowedDummyCode("PORTAL_002");
        
        // Check if a code is allowed
        if (pPortalInfo->IsAllowedDummyCode("PORTAL_001"))
        {
            // Process a portal request
            char result = pPortalInfo->Request(requestType, mapIndex, storeRecordIndex,
                                             userIndex, pRequestData, pUserRights);
        }
        
        // Set up force move HQ notification
        pPortalInfo->SetNotifyForceMoveHQTimer(pTimer);
        pPortalInfo->SetNotifyForceMoveHQState(NotifyForceMoveHQState::Processing);
    }
    
    // Cleanup
    delete pPortalInfo;
}
```

## Request Types

### Supported Request Types
1. **Force Move HQ (Type 1)**: Headquarters force movement operations
2. **Goto Limit Zone (Type 2)**: Movement to limited access zones
3. **Use Move Scroll (Type 3)**: Scroll-based movement operations

## Notification States

### NotifyForceMoveHQState Enum
- **Idle**: No notification in progress
- **Processing**: Notification being processed
- **Completed**: Notification completed

## Dependencies

### Headers Required
- `CMoveMapLimitInfo.h` (base class)
- `CMoveMapLimitRightInfo.h` (for rights checking)
- `CMyTimer.h` (for timer management)
- `CDummy.h` (for dummy object management)

### Related Classes
- **CMoveMapLimitInfo**: Abstract base class
- **CDummy**: Dummy object for portal positions
- **CMyTimer**: Timer for notification processing
- **CMoveMapLimitRightInfo**: User rights and permissions

## Implementation Notes

### Dummy Object Management
- Supports three types of dummy objects: source, destination, and regeneration
- Dummy objects are not owned by this class (no deletion in destructor)
- Provides getter/setter methods for all dummy types

### Allowed Code System
- Uses `std::vector<std::string>` for efficient storage and searching
- Supports dynamic addition and removal of allowed codes
- Provides validation methods for code checking

### Notification System
- State-based notification processing with enum for clarity
- Timer integration for time-based operations
- Processing index tracking for notification management

### Error Handling
- Comprehensive exception handling in all public methods
- Graceful fallbacks for error conditions
- Null pointer checks for all input parameters

## Refactoring Notes

This class was refactored from decompiled C source files to modern C++17/C++20 standards:

### Original Files Consolidated
- `0CMoveMapLimitInfoPortalQEAAIHZ_1403A3EE0.c` (Constructor)
- `1CMoveMapLimitInfoPortalQEAAXZ_1403A3FD0.c` (Destructor)
- `j_RequestCMoveMapLimitInfoPortalUEAAEHHPEADPEAVCMo_14000760D.c` (Request method)
- `LoadINICMoveMapLimitInfoPortalAEAA_NXZ_1403A56F0.c` (LoadINI method)
- Various processing methods for force move, goto limit zone, and move scroll operations

### Modernization Changes
- Converted from C-style to modern C++ derived class design
- Added proper virtual method overrides with `override` keyword
- Implemented STL container usage (`std::vector<std::string>`)
- Added comprehensive error handling and exception safety
- Used modern C++ enum class for type safety
- Added const-correctness and proper method signatures
- Implemented RAII principles for resource management

## Testing Recommendations

1. **Unit Tests**: Test all getter/setter methods and request processing
2. **Integration Tests**: Test interaction with base class and dummy objects
3. **Configuration Tests**: Verify INI loading and configuration management
4. **Notification Tests**: Test state transitions and timer integration
5. **Memory Tests**: Validate proper memory management and no leaks
6. **Error Tests**: Verify graceful handling of invalid inputs and edge cases

## Future Enhancements

- Add configuration validation for loaded INI settings
- Implement caching mechanisms for frequently accessed dummy codes
- Add logging and debugging support for request processing
- Consider adding serialization support for persistent storage
- Implement performance monitoring for notification processing
- Add support for additional request types as needed

## Related Documentation

- [CMoveMapLimitInfo.md](CMoveMapLimitInfo.md) - Abstract base class
- [CMoveMapLimitInfoList.md](CMoveMapLimitInfoList.md) - Container class
- [CMoveMapLimitManager.md](CMoveMapLimitManager.md) - Main manager class
- [CMoveMapLimitRightInfo.md](CMoveMapLimitRightInfo.md) - Rights management
