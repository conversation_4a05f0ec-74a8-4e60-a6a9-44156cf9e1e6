/**
 * @file CMoveMapLimitInfo.cpp
 * @brief Implementation of base class for map movement limitation information
 * <AUTHOR> Development Team
 * @date 2024
 */

#include "../Headers/CMoveMapLimitInfo.h"
#include "../Headers/CMoveMapLimitInfoPortal.h"
#include "../Headers/CMoveMapLimitRightInfo.h"
#include "../../item/Headers/CItemStore.h"
#include <new>

/**
 * @brief Constructor
 * @param uiInx Limitation index
 * @param iType Type of limitation
 * @details Initializes the limitation information with the provided index and type
 */
CMoveMapLimitInfo::CMoveMapLimitInfo(unsigned int uiInx, int iType)
    : m_uiInx(uiInx)
    , m_iMapInx(-1)
    , m_pStoreNPC(nullptr)
    , m_eType(static_cast<LimitType>(iType))
{
    // Member initialization is handled by member initializer list
}

/**
 * @brief Virtual destructor
 * @details Ensures proper cleanup of derived classes
 */
CMoveMapLimitInfo::~CMoveMapLimitInfo()
{
    // Base class destructor - derived classes handle their own cleanup
}

/**
 * @brief Static factory method to create limitation info objects
 * @param uiInx Limitation index
 * @param iType Type of limitation
 * @return Pointer to created limitation info object, or nullptr on failure
 * @details Creates the appropriate derived class based on the limitation type
 */
CMoveMapLimitInfo* CMoveMapLimitInfo::Create(unsigned int uiInx, int iType)
{
    try
    {
        switch (static_cast<LimitType>(iType))
        {
            case LimitType::Portal:
                return new CMoveMapLimitInfoPortal(uiInx, iType);
                
            case LimitType::Zone:
            case LimitType::Map:
                // For now, create portal type for all types
                // This can be extended when other derived classes are implemented
                return new CMoveMapLimitInfoPortal(uiInx, iType);
                
            default:
                return nullptr;
        }
    }
    catch (const std::bad_alloc&)
    {
        return nullptr;
    }
    catch (...)
    {
        return nullptr;
    }
}

/**
 * @brief Check if this limitation matches the specified criteria
 * @param iType Type of limitation to match
 * @param iMapIndex Map index to match
 * @param dwStoreRecordIndex Store record index to match
 * @return true if all criteria match, false otherwise
 * @details Compares the limitation type, map index, and store record index
 */
bool CMoveMapLimitInfo::IsEqualLimit(int iType, int iMapIndex, unsigned int dwStoreRecordIndex) const
{
    // Check if type matches
    if (static_cast<int>(m_eType) != iType)
    {
        return false;
    }

    // Check if map index matches (if set)
    if (m_iMapInx != -1 && m_iMapInx != iMapIndex)
    {
        return false;
    }

    // Check if store record index matches (if we have a store NPC)
    if (m_pStoreNPC && m_uiInx != dwStoreRecordIndex)
    {
        return false;
    }

    return true;
}

/**
 * @brief Get the limitation type
 * @return Type of limitation as integer
 */
int CMoveMapLimitInfo::GetType() const
{
    return static_cast<int>(m_eType);
}

/**
 * @brief Get the limitation index
 * @return Limitation index
 */
unsigned int CMoveMapLimitInfo::GetInx() const
{
    return m_uiInx;
}

/**
 * @brief Get the map index
 * @return Map index (-1 if not set)
 */
int CMoveMapLimitInfo::GetMapIndex() const
{
    return m_iMapInx;
}

/**
 * @brief Get the associated store NPC
 * @return Pointer to store NPC, or nullptr if not set
 */
CItemStore* CMoveMapLimitInfo::GetStoreNPC() const
{
    return m_pStoreNPC;
}

/**
 * @brief Set the map index
 * @param iMapIndex Map index to set
 */
void CMoveMapLimitInfo::SetMapIndex(int iMapIndex)
{
    m_iMapInx = iMapIndex;
}

/**
 * @brief Set the associated store NPC
 * @param pStoreNPC Pointer to store NPC
 */
void CMoveMapLimitInfo::SetStoreNPC(CItemStore* pStoreNPC)
{
    m_pStoreNPC = pStoreNPC;
}
