#pragma once

#include <memory>
#include <vector>
#include <string>
#include <cstdint>

// Forward declarations
class CIniFile;
class CBossMonsterScheduleSystem;
struct BossSchedule;
struct INI_Section;

/**
 * @class BossScheduleMap
 * @brief Manages boss monster scheduling for a specific map
 * 
 * This class handles the loading, saving, and management of boss monster schedules
 * for individual maps in the game world. It maintains a collection of boss schedules
 * and provides functionality to persist them to/from INI files.
 * 
 * Key Features:
 * - Boss schedule management per map
 * - INI file-based persistence
 * - Integration with boss monster schedule system
 * - Memory-safe schedule collection handling
 * 
 * @note Refactored from decompiled BossSchedule_Map structure
 */
class BossScheduleMap {
public:
    // Constants
    static constexpr size_t MAX_MAP_NAME_LENGTH = 64;
    static constexpr size_t OBJECT_SIZE = 0x198; // Original structure size

    /**
     * @brief Default constructor
     * Initializes the boss schedule map with default values
     */
    BossScheduleMap();

    /**
     * @brief Destructor
     * Cleans up all allocated resources and schedules
     */
    ~BossScheduleMap();

    /**
     * @brief Clears all boss schedules and resets the map
     * Safely deallocates all schedule objects and resets internal state
     */
    void Clear();

    /**
     * @brief Loads all boss schedules from the INI file
     * @return true if loading was successful, false otherwise
     * 
     * Reads schedule data from the associated INI file and creates
     * BossSchedule objects for each section found.
     */
    bool LoadAll();

    /**
     * @brief Saves all boss schedules to the INI file
     * @return true if saving was successful, false otherwise
     * 
     * Persists all current boss schedules to the INI file using
     * the boss monster schedule system.
     */
    bool SaveAll();

    // Getters
    const char* GetMapName() const { return m_mapName.c_str(); }
    int GetScheduleCount() const { return m_scheduleCount; }
    int GetMapIndex() const { return m_mapIndex; }
    CBossMonsterScheduleSystem* GetSystem() const { return m_pSystem; }
    const std::vector<BossSchedule*>& GetScheduleList() const { return m_scheduleList; }

    // Setters
    void SetMapName(const char* mapName);
    void SetMapIndex(int index) { m_mapIndex = index; }
    void SetSystem(CBossMonsterScheduleSystem* pSystem) { m_pSystem = pSystem; }

    /**
     * @brief Gets a specific boss schedule by index
     * @param index The index of the schedule to retrieve
     * @return Pointer to the BossSchedule object, or nullptr if index is invalid
     */
    BossSchedule* GetSchedule(int index) const;

    /**
     * @brief Adds a new boss schedule to the map
     * @param pSchedule Pointer to the BossSchedule to add
     * @return true if the schedule was added successfully
     */
    bool AddSchedule(BossSchedule* pSchedule);

    /**
     * @brief Removes a boss schedule by index
     * @param index The index of the schedule to remove
     * @return true if the schedule was removed successfully
     */
    bool RemoveSchedule(int index);

    /**
     * @brief Validates the current state of the boss schedule map
     * @return true if the map is in a valid state
     */
    bool IsValid() const;

    // INI file access
    CIniFile& GetIniFile() { return *m_pIniFile; }
    const CIniFile& GetIniFile() const { return *m_pIniFile; }

private:
    // Member variables (maintaining original structure layout where possible)
    std::unique_ptr<CIniFile> m_pIniFile;           ///< INI file handler for persistence
    std::string m_mapName;                          ///< Map name (originally m_strMap[64])
    CBossMonsterScheduleSystem* m_pSystem;          ///< Pointer to the boss monster schedule system
    std::vector<BossSchedule*> m_scheduleList;      ///< Collection of boss schedules (originally m_ScheduleList)
    int m_scheduleCount;                            ///< Number of active schedules (originally m_nCount)
    int m_mapIndex;                                 ///< Map index (originally m_nIndex)

    // Private helper methods
    void InitializeDefaults();
    void CleanupSchedules();
    bool ValidateSystem() const;
    bool ValidateScheduleIndex(int index) const;

    // Disable copy constructor and assignment operator
    BossScheduleMap(const BossScheduleMap&) = delete;
    BossScheduleMap& operator=(const BossScheduleMap&) = delete;
};

// Utility functions for boss schedule map management
namespace BossScheduleMapUtils {
    /**
     * @brief Creates a new boss schedule map instance
     * @return Unique pointer to the created BossScheduleMap
     */
    std::unique_ptr<BossScheduleMap> CreateBossScheduleMap();

    /**
     * @brief Validates a boss schedule map configuration
     * @param pMap Pointer to the BossScheduleMap to validate
     * @return true if the configuration is valid
     */
    bool ValidateBossScheduleMap(const BossScheduleMap* pMap);

    /**
     * @brief Gets the memory footprint of a boss schedule map
     * @param pMap Pointer to the BossScheduleMap
     * @return Size in bytes of the map's memory usage
     */
    size_t GetMemoryFootprint(const BossScheduleMap* pMap);
}

// Legacy C-style interface for compatibility
extern "C" {
    void BossScheduleMap_Constructor(BossScheduleMap* pThis);
    void BossScheduleMap_Destructor(BossScheduleMap* pThis);
    bool BossScheduleMap_LoadAll(BossScheduleMap* pThis);
    bool BossScheduleMap_SaveAll(BossScheduleMap* pThis);
    void BossScheduleMap_Clear(BossScheduleMap* pThis);
}
