#pragma once

#include <memory>
#include <cstdint>

// Forward declarations
class CCharacter;
class CMapData;
class CGameObject;
struct CGameObjectVtbl;

/**
 * @class CCircleZone
 * @brief Represents a circular zone in the game world for guild battles and objectives
 * 
 * This class manages circular zones that serve as objectives or control points in guild battles.
 * It inherits from CCharacter and provides functionality for zone creation, goal management,
 * position tracking, and network messaging.
 * 
 * Key Features:
 * - Zone state management (inactive, active, destroyed)
 * - Portal index and color association
 * - Position-based proximity detection
 * - Network messaging for zone events
 * - Goal position tracking
 * 
 * @note Refactored from decompiled CCircleZone structure
 */
class CCircleZone : public CCharacter {
public:
    // Zone states
    enum class ZoneState : int {
        INACTIVE = -1,
        ACTIVE = 0,
        GOAL_REACHED = 1,
        DESTROYED = 2
    };

    // Constants
    static constexpr float PROXIMITY_THRESHOLD = 200.0f;
    static constexpr uint8_t INVALID_COLOR = 0xFF;
    static constexpr int INVALID_PORTAL = -1;

    /**
     * @brief Default constructor
     * Initializes the circle zone with default values
     */
    CCircleZone();

    /**
     * @brief Virtual destructor
     * Cleans up resources and calls parent destructor
     */
    virtual ~CCircleZone();

    /**
     * @brief Initializes the circle zone with map and player information
     * @param mapIndex The map index where the zone is located
     * @param playerIndex The player index associated with the zone
     * @param nth The nth instance of the zone
     * @param index The zone index
     * @param pMap Pointer to the map data
     * @return true if initialization was successful
     */
    bool Init(uint32_t mapIndex, int playerIndex, int nth, uint16_t index, CMapData* pMap);

    /**
     * @brief Creates the circle zone with specified parameters
     * @param pMap Pointer to the map data
     * @param color The color identifier for the zone
     * @return true if creation was successful
     */
    bool Create(CMapData* pMap, uint8_t color);

    /**
     * @brief Destroys the circle zone and cleans up resources
     */
    void Destroy();

    /**
     * @brief Processes goal achievement for the zone
     * @param pMap Pointer to the map data
     * @param currentPos Pointer to the current position array
     * @return Result code indicating success or failure
     */
    uint8_t Goal(CMapData* pMap, float* currentPos);

    // Getters
    uint8_t GetColor() const { return m_color; }
    int GetPortalIndex() const { return m_portalIndex; }
    ZoneState GetState() const { return m_state; }
    const float* GetGoalPosition() const { return m_goalPosition; }

    // Setters
    void SetColor(uint8_t color) { m_color = color; }
    void SetPortalIndex(int portalIndex) { m_portalIndex = portalIndex; }
    void SetState(ZoneState state) { m_state = state; }
    void SetGoalPosition(const float* position);

    /**
     * @brief Checks if a position is near the zone
     * @param position Pointer to the position to check
     * @return true if the position is within proximity threshold
     */
    bool IsNearPosition(const float* position) const;

    /**
     * @brief Validates the current state of the circle zone
     * @return true if the zone is in a valid state
     */
    bool IsValid() const;

    /**
     * @brief Gets the distance to a specific position
     * @param position Pointer to the position
     * @return Distance to the position
     */
    float GetDistanceToPosition(const float* position) const;

protected:
    /**
     * @brief Sends network message for zone creation
     */
    void SendMsgCreate();

    /**
     * @brief Sends network message for goal achievement
     */
    void SendMsgGoal();

    /**
     * @brief Initializes default values for member variables
     */
    void InitializeDefaults();

    /**
     * @brief Validates initialization parameters
     * @param pMap Pointer to map data to validate
     * @return true if parameters are valid
     */
    bool ValidateInitParams(CMapData* pMap) const;

private:
    // Member variables (maintaining original structure layout where possible)
    ZoneState m_state;              ///< Current state of the zone (originally m_eState)
    int m_portalIndex;              ///< Portal index associated with the zone (originally m_iPortalInx)
    uint8_t m_color;                ///< Color identifier for the zone (originally m_byColor)
    float* m_goalPosition;          ///< Pointer to goal position (originally m_pkGoalPos)
    
    // Additional members for enhanced functionality
    float m_currentPosition[3];     ///< Current position of the zone (originally m_fCurPos)
    bool m_isInitialized;           ///< Flag indicating if the zone is properly initialized

    // Private helper methods
    void CleanupResources();
    bool AllocateGoalPosition();
    void DeallocateGoalPosition();
    float CalculateDistance(const float* pos1, const float* pos2) const;

    // Disable copy constructor and assignment operator
    CCircleZone(const CCircleZone&) = delete;
    CCircleZone& operator=(const CCircleZone&) = delete;
};

// Utility functions for circle zone management
namespace CCircleZoneUtils {
    /**
     * @brief Creates a new circle zone instance
     * @return Unique pointer to the created CCircleZone
     */
    std::unique_ptr<CCircleZone> CreateCircleZone();

    /**
     * @brief Validates a circle zone configuration
     * @param pZone Pointer to the CCircleZone to validate
     * @return true if the configuration is valid
     */
    bool ValidateCircleZone(const CCircleZone* pZone);

    /**
     * @brief Calculates the squared distance between two positions
     * @param pos1 First position
     * @param pos2 Second position
     * @return Squared distance between positions
     */
    float CalculateSquaredDistance(const float* pos1, const float* pos2);

    /**
     * @brief Checks if a color value is valid
     * @param color Color value to check
     * @return true if the color is valid
     */
    bool IsValidColor(uint8_t color);
}

// Legacy C-style interface for compatibility
extern "C" {
    void CCircleZone_Constructor(CCircleZone* pThis);
    void CCircleZone_Destructor(CCircleZone* pThis);
    bool CCircleZone_Create(CCircleZone* pThis, CMapData* pMap, uint8_t color);
    void CCircleZone_Destroy(CCircleZone* pThis);
    uint8_t CCircleZone_Goal(CCircleZone* pThis, CMapData* pMap, float* currentPos);
    uint8_t CCircleZone_GetColor(CCircleZone* pThis);
    int CCircleZone_GetPortalIndex(CCircleZone* pThis);
    bool CCircleZone_IsNearPosition(CCircleZone* pThis, const float* position);
}
