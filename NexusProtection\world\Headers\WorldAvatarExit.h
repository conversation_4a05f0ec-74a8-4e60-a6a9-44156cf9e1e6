/*
 * WorldAvatarExit.h - World Avatar Exit System
 * Refactored for Visual Studio 2022 compatibility
 * Original: wa_ExitWorldYAXPEAU_CLIDZ_140046190.c
 */

#pragma once

#include <windows.h>
#include <cstdint>
#include <string>
#include <memory>
#include <vector>
#include <functional>

// Forward declarations
class CPartyPlayer;
class CPlayer;

// Client ID structure (modernized from _CLID)
struct ClientID {
    uint16_t wIndex;        // Client index
    uint32_t dwSerial;      // Client serial number
    
    ClientID() : wIndex(0), dwSerial(0) {}
    ClientID(uint16_t index, uint32_t serial) : wIndex(index), dwSerial(serial) {}
    
    bool IsValid() const { return wIndex != 0 && dwSerial != 0; }
    void Reset() { wIndex = 0; dwSerial = 0; }
    bool operator==(const ClientID& other) const {
        return wIndex == other.wIndex && dwSerial == other.dwSerial;
    }
    bool operator!=(const ClientID& other) const {
        return !(*this == other);
    }
};

// Party member information for exit processing
struct PartyMemberInfo {
    CPartyPlayer* pMember;
    uint16_t wZoneIndex;
    bool bIsValid;
    
    PartyMemberInfo() : pMember(nullptr), wZoneIndex(0), bIsValid(false) {}
    PartyMemberInfo(CPartyPlayer* member, uint16_t zone) 
        : pMember(member), wZoneIndex(zone), bIsValid(member != nullptr) {}
};

// Exit result information
struct ExitResult {
    enum class Status {
        Success = 0,
        InvalidClient,
        NotLoggedIn,
        SerialMismatch,
        PartyUpdateFailed,
        NotificationFailed,
        SystemError
    };
    
    Status status;
    std::string message;
    CPartyPlayer* pNewBoss;
    std::vector<PartyMemberInfo> affectedMembers;
    
    ExitResult() : status(Status::Success), pNewBoss(nullptr) {}
    ExitResult(Status s, const std::string& msg = "") 
        : status(s), message(msg), pNewBoss(nullptr) {}
    
    bool IsSuccess() const { return status == Status::Success; }
    const char* GetStatusString() const;
};

/**
 * World Avatar Exit Manager
 * Handles avatar exit from the world system with party management
 */
class WorldAvatarExitManager {
public:
    // Constructor/Destructor
    WorldAvatarExitManager();
    virtual ~WorldAvatarExitManager();

    // Main exit function
    static ExitResult ExitWorld(const ClientID* pClientID);
    
    // Validation functions
    static bool ValidateClientExit(const ClientID* pClientID);
    static bool ValidatePlayerState(const CPartyPlayer* pPlayer, const ClientID* pClientID);
    
    // Party management functions
    static std::vector<PartyMemberInfo> GetPartyMembers(CPartyPlayer* pPlayer);
    static bool NotifyPartyMembers(const std::vector<PartyMemberInfo>& members, 
                                   CPartyPlayer* pLeavingPlayer, 
                                   bool isLeaveNotification = true);
    static bool NotifyNewBoss(const std::vector<PartyMemberInfo>& members, 
                              CPartyPlayer* pNewBoss);
    
    // Player management
    static CPartyPlayer* GetPartyPlayer(uint16_t wIndex);
    static CPlayer* GetPlayer(uint16_t wZoneIndex);
    static bool IsPlayerLoggedIn(const CPartyPlayer* pPlayer);
    static bool IsPartyMode(const CPartyPlayer* pPlayer);
    
    // Party boss management
    static CPartyPlayer* GetPartyBoss(CPartyPlayer* pPlayer);
    static bool ProcessPartyBossChange(CPartyPlayer* pLeavingPlayer, 
                                       CPartyPlayer* pOldBoss, 
                                       CPartyPlayer** ppNewBoss);
    
    // Utility functions
    static bool SendPartyLeaveNotification(CPlayer* pPlayer, 
                                           CPartyPlayer* pLeavingMember);
    static bool SendPartyBossNotification(CPlayer* pPlayer, 
                                          CPartyPlayer* pNewBoss);
    
    // Error handling and logging
    static void LogExitAttempt(const ClientID* pClientID, const ExitResult& result);
    static void LogPartyUpdate(CPartyPlayer* pLeavingPlayer, 
                               CPartyPlayer* pOldBoss, 
                               CPartyPlayer* pNewBoss);

private:
    // Constants
    static constexpr uint16_t MAX_PLAYER_INDEX = 2532;
    static constexpr size_t PARTY_PLAYER_SIZE = 128;
    static constexpr int MAX_PARTY_MEMBERS = 8;

    // Helper methods
    static bool ProcessPartyExit(CPartyPlayer* pPlayer, CPartyPlayer** ppNewBoss);
    static std::vector<PartyMemberInfo> CollectPartyMembers(CPartyPlayer** pPartyMembers);
    static bool ValidatePartyMember(CPartyPlayer* pMember);
    
    // Disable copy constructor and assignment operator
    WorldAvatarExitManager(const WorldAvatarExitManager&) = delete;
    WorldAvatarExitManager& operator=(const WorldAvatarExitManager&) = delete;
};

// Global functions (C-style interface for compatibility)
extern "C" {
    /**
     * Legacy C-style entry point for world avatar exit
     * @param pidWorld - Client identification for the exiting player
     */
    void wa_ExitWorld(const ClientID* pidWorld);
}

// Utility functions
namespace WorldAvatarExitUtils {
    // Client ID utilities
    bool ValidateClientID(const ClientID* pClientID);
    std::string FormatClientInfo(const ClientID* pClientID);
    
    // Party utilities
    bool IsValidPartyMember(CPartyPlayer* pMember);
    size_t CountActivePartyMembers(CPartyPlayer** pPartyMembers);
    CPartyPlayer* FindNextPartyBoss(CPartyPlayer** pPartyMembers, CPartyPlayer* pLeavingPlayer);
    
    // Logging utilities
    std::string FormatExitResult(const ExitResult& result);
    std::string FormatPartyInfo(const std::vector<PartyMemberInfo>& members);
    
    // Error handling
    ExitResult CreateErrorResult(ExitResult::Status status, const std::string& details = "");
    void LogError(const std::string& function, const std::string& error);
    void LogInfo(const std::string& function, const std::string& info);
}

// External references (these would be properly defined in the actual implementation)
extern CPartyPlayer g_PartyPlayer[];  // Global party player array
extern CPlayer g_Player[];            // Global player array

// Function pointer types for external dependencies
using PartyExitFunction = bool(*)(CPartyPlayer*, CPartyPlayer**);
using PartyModeCheckFunction = bool(*)(const CPartyPlayer*);
using PartyMemberGetFunction = CPartyPlayer**(*)(CPartyPlayer*);
using PlayerMessageFunction = void(*)(CPlayer*, CPartyPlayer*, int);

// External function declarations (to be implemented based on actual game classes)
namespace ExternalFunctions {
    extern PartyExitFunction CPartyPlayer_ExitWorld;
    extern PartyModeCheckFunction CPartyPlayer_IsPartyMode;
    extern PartyMemberGetFunction CPartyPlayer_GetPtrPartyMember;
    extern PlayerMessageFunction CPlayer_SendMsg_PartyLeaveSelfResult;
    extern PlayerMessageFunction CPlayer_SendMsg_PartySuccessResult;
}
