/**
 * @file CMoveMapLimitInfoList.cpp
 * @brief Implementation of map movement limitation information list management
 * <AUTHOR> Development Team
 * @date 2024
 */

#include "../Headers/CMoveMapLimitInfoList.h"
#include "../Headers/CMoveMapLimitInfo.h"
#include "../Headers/CMoveMapLimitRightInfo.h"
#include "../../player/Headers/CPlayer.h"
#include <algorithm>
#include <new>

/**
 * @brief Constructor
 * @details Initializes the limitation information list container
 */
CMoveMapLimitInfoList::CMoveMapLimitInfoList()
    : m_vecLimitInfo()
{
    // Vector initialization is handled by member initializer list
}

/**
 * @brief Destructor
 * @details Cleans up all limitation information objects and clears the container
 */
CMoveMapLimitInfoList::~CMoveMapLimitInfoList()
{
    CleanUp();
}

/**
 * @brief Initialize the limitation information list
 * @param vecRightTypeList Vector of right type identifiers for initialization
 * @return true if initialization successful, false otherwise
 * @details Sets up the limitation information list with the provided right types
 */
bool CMoveMapLimitInfoList::Init(const std::vector<int>& vecRightTypeList)
{
    try
    {
        // Clear any existing data
        CleanUp();
        
        // Reserve space for efficiency if we know the expected size
        if (!vecRightTypeList.empty())
        {
            m_vecLimitInfo.reserve(vecRightTypeList.size() * 2); // Estimate
        }
        
        return true;
    }
    catch (...)
    {
        return false;
    }
}

/**
 * @brief Load player-specific limitation information
 * @param pkPlayer Pointer to the player object
 * @param pkRight Pointer to the player's rights information
 * @details Loads and configures limitation information based on player data and rights
 */
void CMoveMapLimitInfoList::Load(CPlayer* pkPlayer, CMoveMapLimitRightInfo* pkRight)
{
    if (!pkPlayer || !pkRight)
    {
        return;
    }

    try
    {
        // Implementation would load limitation data from database or configuration
        // based on player information and rights
        // This is a placeholder for the actual loading logic
        
        // Example: Create limitation info based on player's access rights
        // The actual implementation would query the database or configuration files
    }
    catch (...)
    {
        // Handle any exceptions during loading
        // Log error if logging system is available
    }
}

/**
 * @brief Process a movement limitation request
 * @param iLimitType Type of limitation being requested
 * @param iRequestType Type of request (e.g., check, apply, remove)
 * @param iMapIndex Index of the map
 * @param dwStoreRecordIndex Store record index
 * @param iUserIndex User index
 * @param pRequest Request data buffer
 * @param pkRight User's rights information
 * @return Result of the request processing (0 = failure, non-zero = success)
 * @details Processes movement limitation requests based on user rights and map restrictions
 */
char CMoveMapLimitInfoList::Request(int iLimitType, int iRequestType, int iMapIndex,
                                   unsigned int dwStoreRecordIndex, int iUserIndex, 
                                   char* pRequest, CMoveMapLimitRightInfo* pkRight)
{
    if (!pRequest || !pkRight)
    {
        return 0;
    }

    try
    {
        // Find the appropriate limitation information
        CMoveMapLimitInfo* pLimitInfo = Get(iLimitType, iMapIndex, dwStoreRecordIndex);
        
        if (!pLimitInfo)
        {
            // If no specific limitation exists, create one if needed
            if (CreateLimitInfo(iLimitType, iMapIndex))
            {
                pLimitInfo = Get(iLimitType, iMapIndex, dwStoreRecordIndex);
            }
        }

        if (pLimitInfo)
        {
            // Delegate the request to the specific limitation info object
            return pLimitInfo->Request(iRequestType, iMapIndex, dwStoreRecordIndex, 
                                     iUserIndex, pRequest, pkRight);
        }

        return 0;
    }
    catch (...)
    {
        return 0;
    }
}

/**
 * @brief Get limitation information by criteria
 * @param iLimitType Type of limitation
 * @param iMapIndex Map index
 * @param dwStoreRecordIndex Store record index
 * @return Pointer to the limitation information object, or nullptr if not found
 * @details Searches for and returns the limitation information matching the criteria
 */
CMoveMapLimitInfo* CMoveMapLimitInfoList::Get(int iLimitType, int iMapIndex, 
                                              unsigned int dwStoreRecordIndex)
{
    try
    {
        // Search through the limitation info vector
        for (auto* pLimitInfo : m_vecLimitInfo)
        {
            if (pLimitInfo && pLimitInfo->IsEqualLimit(iLimitType, iMapIndex, dwStoreRecordIndex))
            {
                return pLimitInfo;
            }
        }
        
        return nullptr;
    }
    catch (...)
    {
        return nullptr;
    }
}

/**
 * @brief Clean up all limitation information objects
 * @details Safely deletes all limitation information objects and clears the container
 */
void CMoveMapLimitInfoList::CleanUp()
{
    try
    {
        ClearLimitInfo();
        m_vecLimitInfo.clear();
    }
    catch (...)
    {
        // Handle cleanup errors gracefully
    }
}

/**
 * @brief Get the number of limitation information objects
 * @return Number of objects in the list
 */
size_t CMoveMapLimitInfoList::Size() const
{
    return m_vecLimitInfo.size();
}

/**
 * @brief Check if the list is empty
 * @return true if the list is empty, false otherwise
 */
bool CMoveMapLimitInfoList::Empty() const
{
    return m_vecLimitInfo.empty();
}

/**
 * @brief Clear all limitation information objects
 * @details Helper method to safely delete all objects in the vector
 */
void CMoveMapLimitInfoList::ClearLimitInfo()
{
    for (auto* pLimitInfo : m_vecLimitInfo)
    {
        if (pLimitInfo)
        {
            delete pLimitInfo;
        }
    }
    m_vecLimitInfo.clear();
}

/**
 * @brief Create a new limitation information object
 * @param iLimitType Type of limitation
 * @param iMapIndex Map index
 * @return true if creation successful, false otherwise
 * @details Helper method to create and add new limitation information objects
 */
bool CMoveMapLimitInfoList::CreateLimitInfo(int iLimitType, int iMapIndex)
{
    try
    {
        // Create new limitation info object
        CMoveMapLimitInfo* pNewLimitInfo = CMoveMapLimitInfo::Create(iLimitType, iMapIndex);
        
        if (pNewLimitInfo)
        {
            m_vecLimitInfo.push_back(pNewLimitInfo);
            return true;
        }
        
        return false;
    }
    catch (const std::bad_alloc&)
    {
        return false;
    }
    catch (...)
    {
        return false;
    }
}
