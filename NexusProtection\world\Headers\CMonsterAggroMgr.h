#pragma once

#include <cstdint>
#include <memory>
#include <array>
#include <vector>

// Forward declarations
class CMonster;
class CCharacter;
class AggroCaculateData;

/**
 * @struct CAggroNode
 * @brief Represents an aggro entry for a character
 * 
 * This structure holds aggro-related data for a specific character,
 * including damage dealt, aggro points, and character reference.
 */
struct CAggroNode {
    CCharacter* m_pCharacter;           ///< Pointer to the character
    uint32_t m_dwObjectSerial;          ///< Object serial number for validation
    int m_nAggroData;                   ///< Current aggro points
    int m_nDamageData;                  ///< Total damage dealt
    int m_nKingPowerDamage;             ///< King power damage dealt

    /**
     * @brief Default constructor
     */
    CAggroNode();

    /**
     * @brief Initializes the aggro node
     */
    void Init();

    /**
     * @brief Sets the character for this aggro node
     * @param pCharacter Pointer to the character
     */
    void Set(CCharacter* pCharacter);

    /**
     * @brief Checks if this aggro node is active
     * @return true if the node has a valid character
     */
    bool IsLive() const;
};

/**
 * @class CMonsterAggroMgr
 * @brief Manages aggro system for monsters
 * 
 * This class handles the aggro (threat) system for monsters, tracking
 * which characters have generated threat and managing target selection
 * based on aggro values, damage dealt, and other factors.
 * 
 * Key Features:
 * - Aggro pool management with fixed-size array
 * - Top aggro and damage tracking
 * - Automatic aggro decay and reset timers
 * - Short-term aggro ranking updates
 * - King power damage tracking
 * 
 * @note Refactored from decompiled CMonsterAggroMgr structure
 */
class CMonsterAggroMgr {
public:
    // Constants
    static constexpr int MAX_AGGRO_NODES = 10;
    static constexpr uint32_t DEFAULT_ALL_RESET_TIMER = 30000;  // 30 seconds
    static constexpr uint32_t DEFAULT_SHORT_RANK_TIMER = 5000;  // 5 seconds
    static constexpr int INVALID_AGGRO_INDEX = -1;
    static constexpr float MAX_AGGRO_DISTANCE = 1000.0f;

    /**
     * @brief Default constructor
     * Initializes the aggro manager with default values
     */
    CMonsterAggroMgr();

    /**
     * @brief Destructor
     * Cleans up resources
     */
    ~CMonsterAggroMgr();

    /**
     * @brief Initializes the aggro manager
     * Resets all aggro nodes and timers
     */
    void Init();

    /**
     * @brief One-time initialization with monster reference
     * @param pMonster Pointer to the monster this aggro manager belongs to
     */
    void OnlyOnceInit(CMonster* pMonster);

    /**
     * @brief Main processing function called each frame
     * Handles aggro decay, ranking updates, and timer management
     */
    void Process();

    /**
     * @brief Sets aggro for a character
     * @param pCharacter Pointer to the character
     * @param damage Damage dealt
     * @param attackType Type of attack
     * @param attackSerial Attack serial number
     * @param otherPlayerSupport Whether other player support is involved
     * @param tempSkill Whether this is from a temporary skill
     */
    void SetAggro(CCharacter* pCharacter, int damage, int attackType, 
                  uint32_t attackSerial, int otherPlayerSupport, int tempSkill);

    /**
     * @brief Resets all aggro data
     */
    void ResetAggro();

    /**
     * @brief Delays the short rank timer
     * @param delayTime Time to delay in milliseconds
     */
    void ShortRankDelay(uint32_t delayTime);

    /**
     * @brief Searches for an aggro node by character
     * @param pCharacter Pointer to the character to search for
     * @return Pointer to the aggro node, or nullptr if not found
     */
    CAggroNode* SearchAggroNode(CCharacter* pCharacter);

    /**
     * @brief Gets the character with the highest aggro
     * @return Pointer to the top aggro character, or nullptr if none
     */
    CCharacter* GetTopAggroCharacter();

    /**
     * @brief Gets the character with the highest damage
     * @return Pointer to the top damage character, or nullptr if none
     */
    CCharacter* GetTopDamageCharacter();

    /**
     * @brief Gets the character with the highest king power damage
     * @return Pointer to the king power damage character, or nullptr if none
     */
    CCharacter* GetKingPowerDamageCharacter();

    // Getters
    uint32_t GetAggroCount() const { return m_dwAggroCount; }
    uint32_t GetAllResetTimer() const { return m_dwAllResetTimer; }
    uint32_t GetShortRankTimer() const { return m_dwShortRankTimer; }
    CMonster* GetMonster() const { return m_pMonster; }
    const std::array<CAggroNode, MAX_AGGRO_NODES>& GetAggroPool() const { return m_AggroPool; }

    // Setters
    void SetAllResetTimer(uint32_t timer) { m_dwAllResetTimer = timer; }
    void SetShortRankTimer(uint32_t timer) { m_dwShortRankTimer = timer; }

    /**
     * @brief Validates the current state of the aggro manager
     * @return true if the manager is in a valid state
     */
    bool IsValid() const;

protected:
    /**
     * @brief Performs short-term aggro ranking
     * Updates top aggro and damage characters
     */
    void _ShortRank();

    /**
     * @brief Finds an empty aggro node
     * @return Pointer to an empty node, or nullptr if all are occupied
     */
    CAggroNode* _GetBlinkNode();

    /**
     * @brief Searches for an aggro node by character (internal)
     * @param pCharacter Pointer to the character to search for
     * @return Pointer to the aggro node, or nullptr if not found
     */
    CAggroNode* _SearchAggroNode(CCharacter* pCharacter);

    /**
     * @brief Sends aggro change data to clients
     */
    void SendChangeAggroData();

    /**
     * @brief Validates character state for aggro tracking
     * @param pCharacter Pointer to the character to validate
     * @return true if the character is valid for aggro tracking
     */
    bool ValidateCharacter(CCharacter* pCharacter) const;

    /**
     * @brief Calculates aggro bonus based on area and distance
     * @param pCharacter Pointer to the character
     * @return Aggro bonus value
     */
    int CalculateAggroBonus(CCharacter* pCharacter) const;

private:
    // Member variables (maintaining original structure layout where possible)
    CCharacter* m_pTopAggroCharacter;               ///< Character with highest aggro
    CCharacter* m_pTopDamageCharacter;              ///< Character with highest damage
    CCharacter* m_pKingPowerDamageCharacter;        ///< Character with highest king power damage
    std::array<CAggroNode, MAX_AGGRO_NODES> m_AggroPool; ///< Pool of aggro nodes
    uint32_t m_dwAggroCount;                        ///< Number of active aggro entries
    uint32_t m_dwAllResetLastTime;                  ///< Last time all aggro was reset
    uint32_t m_dwShortRankLastTime;                 ///< Last time short ranking was performed
    uint32_t m_dwAllResetTimer;                     ///< Timer for full aggro reset
    uint32_t m_dwShortRankTimer;                    ///< Timer for short ranking updates
    CMonster* m_pMonster;                           ///< Pointer to the owning monster

    // Private helper methods
    void InitializeDefaults();
    void CleanupResources();
    void UpdateAggroCount();
    bool IsCharacterValid(CCharacter* pCharacter) const;
    void ProcessAggroDecay();
    void ProcessRankingUpdates();

    // Disable copy constructor and assignment operator
    CMonsterAggroMgr(const CMonsterAggroMgr&) = delete;
    CMonsterAggroMgr& operator=(const CMonsterAggroMgr&) = delete;
};

// Utility functions for aggro management
namespace CMonsterAggroMgrUtils {
    /**
     * @brief Creates a new aggro manager instance
     * @return Unique pointer to the created CMonsterAggroMgr
     */
    std::unique_ptr<CMonsterAggroMgr> CreateAggroManager();

    /**
     * @brief Validates an aggro manager configuration
     * @param pMgr Pointer to the CMonsterAggroMgr to validate
     * @return true if the configuration is valid
     */
    bool ValidateAggroManager(const CMonsterAggroMgr* pMgr);

    /**
     * @brief Gets the memory footprint of an aggro manager
     * @param pMgr Pointer to the CMonsterAggroMgr
     * @return Size in bytes of the manager's memory usage
     */
    size_t GetMemoryFootprint(const CMonsterAggroMgr* pMgr);

    /**
     * @brief Calculates aggro priority based on damage and other factors
     * @param damage Damage dealt
     * @param attackType Type of attack
     * @param distance Distance to target
     * @return Calculated aggro priority
     */
    int CalculateAggroPriority(int damage, int attackType, float distance);
}

// Legacy C-style interface for compatibility
extern "C" {
    void CMonsterAggroMgr_Constructor(CMonsterAggroMgr* pThis);
    void CMonsterAggroMgr_Destructor(CMonsterAggroMgr* pThis);
    void CMonsterAggroMgr_Init(CMonsterAggroMgr* pThis);
    void CMonsterAggroMgr_OnlyOnceInit(CMonsterAggroMgr* pThis, CMonster* pMonster);
    void CMonsterAggroMgr_Process(CMonsterAggroMgr* pThis);
    void CMonsterAggroMgr_SetAggro(CMonsterAggroMgr* pThis, CCharacter* pCharacter, 
                                   int damage, int attackType, uint32_t attackSerial, 
                                   int otherPlayerSupport, int tempSkill);
    void CMonsterAggroMgr_ResetAggro(CMonsterAggroMgr* pThis);
    CCharacter* CMonsterAggroMgr_GetTopAggroCharacter(CMonsterAggroMgr* pThis);
    CCharacter* CMonsterAggroMgr_GetTopDamageCharacter(CMonsterAggroMgr* pThis);
    CAggroNode* CMonsterAggroMgr_SearchAggroNode(CMonsterAggroMgr* pThis, CCharacter* pCharacter);
}
