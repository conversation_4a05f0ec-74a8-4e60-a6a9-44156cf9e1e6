# CMakeLists.txt for NexusProtection Project
# Modern CMake configuration with automatic file discovery
# Compatible with Visual Studio 2022

cmake_minimum_required(VERSION 3.20)

# Project configuration
project(NexusProtection 
    VERSION 1.0.0
    DESCRIPTION "NexusProtection - Unified Zoneserver Project"
    LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Platform-specific settings for Windows/Visual Studio
if(WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-DNOMINMAX)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
    add_definitions(-DUNICODE)
    add_definitions(-D_UNICODE)
endif()

# Compiler-specific settings for MSVC
if(MSVC)
    # Use static runtime library
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
    
    # Warning level and other flags
    add_compile_options(/W3)
    add_compile_options(/permissive-)
    add_compile_options(/Zc:__cplusplus)
    add_compile_options(/MP)  # Multi-processor compilation
    
    # Debug configuration
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi /RTC1")
    
    # Release configuration
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
endif()

# Define all modules
set(NEXUS_MODULES
    world
    authentication
    combat
    database
    economy
    items
    network
    player
    security
    system
)

# Initialize collections for all files
set(ALL_SOURCES "")
set(ALL_HEADERS "")
set(ALL_INCLUDE_DIRS "")

# Function to discover files in a module
function(discover_module_files MODULE_NAME)
    set(MODULE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/${MODULE_NAME}")
    
    if(EXISTS "${MODULE_DIR}")
        # Find source files
        file(GLOB_RECURSE MODULE_SOURCES 
            "${MODULE_DIR}/Source/*.cpp"
            "${MODULE_DIR}/Source/*.c"
        )
        
        # Find header files
        file(GLOB_RECURSE MODULE_HEADERS 
            "${MODULE_DIR}/Headers/*.h"
            "${MODULE_DIR}/Headers/*.hpp"
        )
        
        # Add to global collections
        list(APPEND ALL_SOURCES ${MODULE_SOURCES})
        list(APPEND ALL_HEADERS ${MODULE_HEADERS})
        
        # Add include directory
        if(EXISTS "${MODULE_DIR}/Headers")
            list(APPEND ALL_INCLUDE_DIRS "${MODULE_DIR}/Headers")
        endif()
        
        # Update parent scope
        set(ALL_SOURCES ${ALL_SOURCES} PARENT_SCOPE)
        set(ALL_HEADERS ${ALL_HEADERS} PARENT_SCOPE)
        set(ALL_INCLUDE_DIRS ${ALL_INCLUDE_DIRS} PARENT_SCOPE)
        
        # Report findings
        list(LENGTH MODULE_SOURCES source_count)
        list(LENGTH MODULE_HEADERS header_count)
        
        if(source_count GREATER 0 OR header_count GREATER 0)
            message(STATUS "Module '${MODULE_NAME}': ${source_count} sources, ${header_count} headers")
            
            if(source_count GREATER 0)
                foreach(source_file ${MODULE_SOURCES})
                    get_filename_component(filename ${source_file} NAME)
                    message(STATUS "  Source: ${filename}")
                endforeach()
            endif()
            
            if(header_count GREATER 0)
                foreach(header_file ${MODULE_HEADERS})
                    get_filename_component(filename ${header_file} NAME)
                    message(STATUS "  Header: ${filename}")
                endforeach()
            endif()
        else()
            message(STATUS "Module '${MODULE_NAME}': No files found")
        endif()
    else()
        message(STATUS "Module '${MODULE_NAME}': Directory not found")
    endif()
endfunction()

# Discover all modules
message(STATUS "=== NexusProtection Module Discovery ===")
foreach(MODULE ${NEXUS_MODULES})
    discover_module_files(${MODULE})
endforeach()

# Remove duplicates
list(REMOVE_DUPLICATES ALL_SOURCES)
list(REMOVE_DUPLICATES ALL_HEADERS)
list(REMOVE_DUPLICATES ALL_INCLUDE_DIRS)

# Report totals
list(LENGTH ALL_SOURCES total_sources)
list(LENGTH ALL_HEADERS total_headers)
list(LENGTH ALL_INCLUDE_DIRS total_includes)

message(STATUS "=== Total Project Files ===")
message(STATUS "Total Sources: ${total_sources}")
message(STATUS "Total Headers: ${total_headers}")
message(STATUS "Include Directories: ${total_includes}")

# Ensure we have files to build
if(total_sources EQUAL 0)
    message(WARNING "No source files found! Please ensure modules contain Source/*.cpp files")
    # Create a dummy source file to prevent build errors
    file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/dummy.cpp" "// Dummy file for empty project\n")
    list(APPEND ALL_SOURCES "${CMAKE_CURRENT_BINARY_DIR}/dummy.cpp")
endif()

# Create the main static library
add_library(NexusProtection STATIC
    ${ALL_SOURCES}
    ${ALL_HEADERS}
)

# Set include directories
target_include_directories(NexusProtection
    PUBLIC
        ${ALL_INCLUDE_DIRS}
        ${CMAKE_CURRENT_SOURCE_DIR}
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link libraries
if(WIN32)
    target_link_libraries(NexusProtection
        PRIVATE
            kernel32
            user32
            advapi32
            ws2_32
            winmm
    )
endif()

# Preprocessor definitions
target_compile_definitions(NexusProtection
    PRIVATE
        $<$<CONFIG:Debug>:_DEBUG>
        $<$<CONFIG:Release>:NDEBUG>
        NEXUS_PROTECTION_EXPORTS
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        UNICODE
        _UNICODE
)

# Set target properties
set_target_properties(NexusProtection PROPERTIES
    OUTPUT_NAME "NexusProtection"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    FOLDER "NexusProtection"
)

# Create source groups for IDE organization
foreach(MODULE ${NEXUS_MODULES})
    set(MODULE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/${MODULE}")
    
    if(EXISTS "${MODULE_DIR}")
        file(GLOB_RECURSE MODULE_SOURCES "${MODULE_DIR}/Source/*.cpp" "${MODULE_DIR}/Source/*.c")
        file(GLOB_RECURSE MODULE_HEADERS "${MODULE_DIR}/Headers/*.h" "${MODULE_DIR}/Headers/*.hpp")
        
        if(MODULE_SOURCES)
            source_group("Source Files\\${MODULE}" FILES ${MODULE_SOURCES})
        endif()
        
        if(MODULE_HEADERS)
            source_group("Header Files\\${MODULE}" FILES ${MODULE_HEADERS})
        endif()
    endif()
endforeach()

# Install rules
install(TARGETS NexusProtection
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers maintaining directory structure
foreach(MODULE ${NEXUS_MODULES})
    set(MODULE_HEADERS_DIR "${CMAKE_CURRENT_SOURCE_DIR}/${MODULE}/Headers")
    if(EXISTS "${MODULE_HEADERS_DIR}")
        install(DIRECTORY "${MODULE_HEADERS_DIR}/"
            DESTINATION "include/${MODULE}"
            FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
        )
    endif()
endforeach()

# Optional: Create test executables for modules that have tests
foreach(MODULE ${NEXUS_MODULES})
    set(MODULE_TEST_DIR "${CMAKE_CURRENT_SOURCE_DIR}/${MODULE}/Tests")
    if(EXISTS "${MODULE_TEST_DIR}")
        file(GLOB_RECURSE MODULE_TEST_SOURCES "${MODULE_TEST_DIR}/*.cpp")
        if(MODULE_TEST_SOURCES)
            add_executable(${MODULE}Test ${MODULE_TEST_SOURCES})
            target_link_libraries(${MODULE}Test PRIVATE NexusProtection)
            target_include_directories(${MODULE}Test PRIVATE ${ALL_INCLUDE_DIRS})
            set_target_properties(${MODULE}Test PROPERTIES
                FOLDER "NexusProtection/Tests"
            )
            
            if(BUILD_TESTING)
                enable_testing()
                add_test(NAME ${MODULE}Test COMMAND ${MODULE}Test)
            endif()
        endif()
    endif()
endforeach()

# Print final configuration summary
message(STATUS "=== NexusProtection Build Configuration ===")
message(STATUS "  Project: ${PROJECT_NAME} v${PROJECT_VERSION}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
if(MSVC)
    message(STATUS "  MSVC Version: ${MSVC_VERSION}")
    message(STATUS "  Platform Toolset: v143")
endif()
message(STATUS "  Modules: ${NEXUS_MODULES}")
message(STATUS "  Total Source Files: ${total_sources}")
message(STATUS "  Total Header Files: ${total_headers}")
message(STATUS "=============================================")
