/**
 * @file CMoveMapLimitInfoPortal.cpp
 * @brief Implementation of portal-specific map movement limitation information
 * <AUTHOR> Development Team
 * @date 2024
 */

#include "../Headers/CMoveMapLimitInfoPortal.h"
#include "../Headers/CMoveMapLimitRightInfo.h"
#include "../../timer/Headers/CMyTimer.h"
#include "../../dummy/Headers/CDummy.h"
#include <algorithm>
#include <new>

/**
 * @brief Constructor
 * @param uiInx Limitation index
 * @param iType Type of limitation
 * @details Initializes the portal-specific limitation information
 */
CMoveMapLimitInfoPortal::CMoveMapLimitInfoPortal(unsigned int uiInx, int iType)
    : CMoveMapLimitInfo(uiInx, iType)
    , m_pkSrcDummy(nullptr)
    , m_pkDestDummy(nullptr)
    , m_pkRegenDummy(nullptr)
    , m_vecAllowDummyCode()
    , m_eNotifyForceMoveHQState(NotifyForceMoveHQState::Idle)
    , m_pkNotifyForceMoveHQTimer(nullptr)
    , m_uiProcNotifyInx(0)
{
    InitializeMembers();
}

/**
 * @brief Destructor
 * @details Cleans up all portal-specific resources
 */
CMoveMapLimitInfoPortal::~CMoveMapLimitInfoPortal()
{
    CleanupTimers();
    ClearAllowedDummyCodes();
}

/**
 * @brief Process a movement limitation request
 * @param iRequestType Type of request
 * @param iMapIndex Map index
 * @param dwStoreRecordIndex Store record index
 * @param iUserIndex User index
 * @param pRequest Request data buffer
 * @param pkRight User's rights information
 * @return Result of the request processing (0 = failure, non-zero = success)
 * @details Processes portal-specific movement limitation requests
 */
char CMoveMapLimitInfoPortal::Request(int iRequestType, int iMapIndex, unsigned int dwStoreRecordIndex,
                                     int iUserIndex, char* pRequest, CMoveMapLimitRightInfo* pkRight)
{
    if (!pRequest || !pkRight)
    {
        return 0;
    }

    try
    {
        // Process different types of portal requests
        switch (iRequestType)
        {
            case 1: // Force move HQ request
                return ProcForceMoveHQ(pRequest);
                
            case 2: // Goto limit zone request
                return ProcGotoLimitZone(pRequest);
                
            case 3: // Use move scroll request
                return ProcUseMoveScroll(pRequest);
                
            default:
                return 0;
        }
    }
    catch (...)
    {
        return 0;
    }
}

/**
 * @brief Load portal configuration from INI file
 * @return true if loading successful, false otherwise
 * @details Loads portal-specific configuration including allowed dummy codes
 */
bool CMoveMapLimitInfoPortal::LoadINI()
{
    try
    {
        // Implementation would load from INI file
        // This is a placeholder for the actual loading logic
        
        // Example: Load allowed dummy codes from configuration
        // The actual implementation would read from an INI file
        
        return true;
    }
    catch (...)
    {
        return false;
    }
}

/**
 * @brief Set the source dummy object
 * @param pDummy Pointer to source dummy object
 */
void CMoveMapLimitInfoPortal::SetSrcDummy(CDummy* pDummy)
{
    m_pkSrcDummy = pDummy;
}

/**
 * @brief Set the destination dummy object
 * @param pDummy Pointer to destination dummy object
 */
void CMoveMapLimitInfoPortal::SetDestDummy(CDummy* pDummy)
{
    m_pkDestDummy = pDummy;
}

/**
 * @brief Set the regeneration dummy object
 * @param pDummy Pointer to regeneration dummy object
 */
void CMoveMapLimitInfoPortal::SetRegenDummy(CDummy* pDummy)
{
    m_pkRegenDummy = pDummy;
}

/**
 * @brief Get the source dummy object
 * @return Pointer to source dummy object
 */
CDummy* CMoveMapLimitInfoPortal::GetSrcDummy() const
{
    return m_pkSrcDummy;
}

/**
 * @brief Get the destination dummy object
 * @return Pointer to destination dummy object
 */
CDummy* CMoveMapLimitInfoPortal::GetDestDummy() const
{
    return m_pkDestDummy;
}

/**
 * @brief Get the regeneration dummy object
 * @return Pointer to regeneration dummy object
 */
CDummy* CMoveMapLimitInfoPortal::GetRegenDummy() const
{
    return m_pkRegenDummy;
}

/**
 * @brief Add an allowed dummy code
 * @param code Dummy code to add to allowed list
 */
void CMoveMapLimitInfoPortal::AddAllowedDummyCode(const std::string& code)
{
    if (!code.empty())
    {
        m_vecAllowDummyCode.push_back(code);
    }
}

/**
 * @brief Clear all allowed dummy codes
 */
void CMoveMapLimitInfoPortal::ClearAllowedDummyCodes()
{
    m_vecAllowDummyCode.clear();
}

/**
 * @brief Check if a dummy code is allowed
 * @param code Dummy code to check
 * @return true if the code is allowed, false otherwise
 */
bool CMoveMapLimitInfoPortal::IsAllowedDummyCode(const std::string& code) const
{
    return std::find(m_vecAllowDummyCode.begin(), m_vecAllowDummyCode.end(), code) 
           != m_vecAllowDummyCode.end();
}

/**
 * @brief Get the number of allowed dummy codes
 * @return Number of allowed dummy codes
 */
size_t CMoveMapLimitInfoPortal::GetAllowedDummyCodeCount() const
{
    return m_vecAllowDummyCode.size();
}

/**
 * @brief Set the notify force move HQ state
 * @param state New state to set
 */
void CMoveMapLimitInfoPortal::SetNotifyForceMoveHQState(NotifyForceMoveHQState state)
{
    m_eNotifyForceMoveHQState = state;
}

/**
 * @brief Get the notify force move HQ state
 * @return Current notify force move HQ state
 */
NotifyForceMoveHQState CMoveMapLimitInfoPortal::GetNotifyForceMoveHQState() const
{
    return m_eNotifyForceMoveHQState;
}

/**
 * @brief Set the notify force move HQ timer
 * @param pTimer Pointer to timer object
 */
void CMoveMapLimitInfoPortal::SetNotifyForceMoveHQTimer(CMyTimer* pTimer)
{
    m_pkNotifyForceMoveHQTimer = pTimer;
}

/**
 * @brief Get the notify force move HQ timer
 * @return Pointer to timer object
 */
CMyTimer* CMoveMapLimitInfoPortal::GetNotifyForceMoveHQTimer() const
{
    return m_pkNotifyForceMoveHQTimer;
}

/**
 * @brief Set the processing notification index
 * @param uiInx Processing notification index
 */
void CMoveMapLimitInfoPortal::SetProcNotifyInx(unsigned int uiInx)
{
    m_uiProcNotifyInx = uiInx;
}

/**
 * @brief Get the processing notification index
 * @return Processing notification index
 */
unsigned int CMoveMapLimitInfoPortal::GetProcNotifyInx() const
{
    return m_uiProcNotifyInx;
}

/**
 * @brief Process force move HQ request
 * @param pRequest Request data buffer
 * @return Result of processing (0 = failure, non-zero = success)
 */
char CMoveMapLimitInfoPortal::ProcForceMoveHQ(char* pRequest)
{
    if (!pRequest)
    {
        return 0;
    }

    try
    {
        // Implementation would process force move HQ request
        // This is a placeholder for the actual processing logic
        
        // Set state to processing
        SetNotifyForceMoveHQState(NotifyForceMoveHQState::Processing);
        
        // Delegate to sub-processing
        SubProcForceMoveHQ();
        
        return 1;
    }
    catch (...)
    {
        return 0;
    }
}

/**
 * @brief Process goto limit zone request
 * @param pRequest Request data buffer
 * @return Result of processing (0 = failure, non-zero = success)
 */
char CMoveMapLimitInfoPortal::ProcGotoLimitZone(char* pRequest)
{
    if (!pRequest)
    {
        return 0;
    }

    try
    {
        // Delegate to sub-processing
        return SubProcGotoLimitZone();
    }
    catch (...)
    {
        return 0;
    }
}

/**
 * @brief Process use move scroll request
 * @param pRequest Request data buffer
 * @return Result of processing (0 = failure, non-zero = success)
 */
char CMoveMapLimitInfoPortal::ProcUseMoveScroll(char* pRequest)
{
    if (!pRequest)
    {
        return 0;
    }

    try
    {
        // Implementation would process move scroll usage
        // This is a placeholder for the actual processing logic
        
        return 1;
    }
    catch (...)
    {
        return 0;
    }
}

/**
 * @brief Sub-process force move HQ
 * @details Handles the detailed processing of force move HQ requests
 */
void CMoveMapLimitInfoPortal::SubProcForceMoveHQ()
{
    try
    {
        // Implementation would handle detailed force move HQ processing
        // This is a placeholder for the actual sub-processing logic
        
        // Trigger notification processing
        SubProcNotifyForceMoveHQ();
    }
    catch (...)
    {
        // Handle errors gracefully
    }
}

/**
 * @brief Sub-process goto limit zone
 * @return Result of processing (0 = failure, non-zero = success)
 * @details Handles the detailed processing of goto limit zone requests
 */
char CMoveMapLimitInfoPortal::SubProcGotoLimitZone()
{
    try
    {
        // Implementation would handle detailed goto limit zone processing
        // This is a placeholder for the actual sub-processing logic
        
        return 1;
    }
    catch (...)
    {
        return 0;
    }
}

/**
 * @brief Sub-process notify force move HQ
 * @details Handles the notification processing for force move HQ
 */
void CMoveMapLimitInfoPortal::SubProcNotifyForceMoveHQ()
{
    try
    {
        // Implementation would handle notification processing
        // This is a placeholder for the actual notification logic
        
        // Set state to completed
        SetNotifyForceMoveHQState(NotifyForceMoveHQState::Completed);
    }
    catch (...)
    {
        // Handle errors gracefully
    }
}

/**
 * @brief Initialize member variables
 * @details Helper method to initialize all member variables to default values
 */
void CMoveMapLimitInfoPortal::InitializeMembers()
{
    // Reserve some space for allowed dummy codes for efficiency
    m_vecAllowDummyCode.reserve(10);
}

/**
 * @brief Cleanup timer resources
 * @details Helper method to safely cleanup timer objects
 */
void CMoveMapLimitInfoPortal::CleanupTimers()
{
    // Note: We don't delete the timer here as it might be managed elsewhere
    // Just set the pointer to null
    m_pkNotifyForceMoveHQTimer = nullptr;
}
