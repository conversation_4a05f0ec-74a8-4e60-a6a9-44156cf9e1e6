#include "../Headers/MonsterSFContDamageTolerance.h"
#include <cstring>
#include <cmath>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cassert>
#include <cstdlib>

// Include necessary headers for dependencies
// These would be replaced with actual headers in the real implementation
class CMonster {
public:
    bool m_bLive;  // Is alive flag
    
    static int GetMyDMGSFContCount(CMonster* pMonster);
};

// Global utility functions
extern uint32_t GetLoopTime();
extern float ffloor(float value);
extern int rand();

// MonsterSFContDamageTolerance implementation

MonsterSFContDamageTolerance::MonsterSFContDamageTolerance()
    : m_pMonster(nullptr)
    , m_fToleranceProb(DEFAULT_TOLERANCE)
    , m_dwLastUpdateTime(INVALID_TIME)
    , m_fToleranceProbMax(DEFAULT_TOLERANCE)
{
    InitializeDefaults();
}

MonsterSFContDamageTolerance::MonsterSFContDamageTolerance(const MonsterSFContDamageTolerance& other)
    : m_pMonster(other.m_pMonster)
    , m_fToleranceProb(other.m_fToleranceProb)
    , m_dwLastUpdateTime(other.m_dwLastUpdateTime)
    , m_fToleranceProbMax(other.m_fToleranceProbMax)
{
}

MonsterSFContDamageTolerance::~MonsterSFContDamageTolerance()
{
    CleanupResources();
}

void MonsterSFContDamageTolerance::InitializeDefaults()
{
    m_pMonster = nullptr;
    m_fToleranceProb = DEFAULT_TOLERANCE;
    m_dwLastUpdateTime = INVALID_TIME;
    m_fToleranceProbMax = DEFAULT_TOLERANCE;
}

void MonsterSFContDamageTolerance::CleanupResources()
{
    // No dynamic resources to clean up, just reset pointers
    m_pMonster = nullptr;
}

void MonsterSFContDamageTolerance::Init(float maxToleranceValue)
{
    m_dwLastUpdateTime = GetCurrentTime();
    
    // Validate and clamp the maximum tolerance value
    float validatedMax = ValidateTolerance(maxToleranceValue);
    m_fToleranceProbMax = validatedMax;
    m_fToleranceProb = validatedMax;
}

void MonsterSFContDamageTolerance::OnlyOnceInit(CMonster* pMonster)
{
    m_pMonster = pMonster;
}

void MonsterSFContDamageTolerance::Update()
{
    if (!IsMonsterValid()) {
        return;
    }
    
    uint32_t currentTime = GetCurrentTime();
    
    // Check if monster has active continuous damage effects
    int contDamageCount = CMonster::GetMyDMGSFContCount(m_pMonster);
    
    if (contDamageCount <= 0) {
        // No continuous damage effects, check for recovery
        if (ShouldRecover()) {
            ProcessRecovery();
            m_dwLastUpdateTime = currentTime;
        }
    } else {
        // Has continuous damage effects, update time but don't recover
        m_dwLastUpdateTime = currentTime;
    }
}

bool MonsterSFContDamageTolerance::IsSFContDamage()
{
    // Calculate tolerance percentage (0-100)
    int tolerancePercent = static_cast<int>(ffloor(m_fToleranceProb * 100.0f));
    
    // Generate random percentage (0-99)
    int randomPercent = GenerateRandomPercent();
    
    // Return true if random value is greater than tolerance (damage should be applied)
    return tolerancePercent <= randomPercent;
}

void MonsterSFContDamageTolerance::SetSFDamageTolerance_Variation(float addValue)
{
    m_fToleranceProb += addValue;
    
    // Clamp to valid range
    if (m_fToleranceProb > m_fToleranceProbMax) {
        m_fToleranceProb = m_fToleranceProbMax;
    }
    
    if (m_fToleranceProb < MIN_TOLERANCE) {
        m_fToleranceProb = MIN_TOLERANCE;
    }
}

void MonsterSFContDamageTolerance::SetToleranceProb(float tolerance)
{
    m_fToleranceProb = ValidateTolerance(tolerance);
}

void MonsterSFContDamageTolerance::SetToleranceProbMax(float maxTolerance)
{
    m_fToleranceProbMax = ValidateTolerance(maxTolerance);
    
    // Ensure current tolerance doesn't exceed new maximum
    if (m_fToleranceProb > m_fToleranceProbMax) {
        m_fToleranceProb = m_fToleranceProbMax;
    }
}

void MonsterSFContDamageTolerance::ResetToleranceToMax()
{
    m_fToleranceProb = m_fToleranceProbMax;
}

bool MonsterSFContDamageTolerance::IsValid() const
{
    return m_fToleranceProb >= MIN_TOLERANCE && 
           m_fToleranceProb <= MAX_TOLERANCE &&
           m_fToleranceProbMax >= MIN_TOLERANCE && 
           m_fToleranceProbMax <= MAX_TOLERANCE &&
           m_fToleranceProb <= m_fToleranceProbMax;
}

std::string MonsterSFContDamageTolerance::ToString() const
{
    std::stringstream ss;
    ss << "MonsterSFContDamageTolerance:" << std::endl;
    ss << "  Current Tolerance: " << std::fixed << std::setprecision(3) << m_fToleranceProb << std::endl;
    ss << "  Maximum Tolerance: " << std::fixed << std::setprecision(3) << m_fToleranceProbMax << std::endl;
    ss << "  Last Update Time: " << m_dwLastUpdateTime << std::endl;
    ss << "  Monster Valid: " << (IsMonsterValid() ? "Yes" : "No") << std::endl;
    ss << "  Tolerance Percentage: " << static_cast<int>(m_fToleranceProb * 100.0f) << "%" << std::endl;
    return ss.str();
}

MonsterSFContDamageTolerance& MonsterSFContDamageTolerance::operator=(const MonsterSFContDamageTolerance& rhs)
{
    if (this != &rhs) {
        m_pMonster = rhs.m_pMonster;
        m_fToleranceProb = rhs.m_fToleranceProb;
        m_dwLastUpdateTime = rhs.m_dwLastUpdateTime;
        m_fToleranceProbMax = rhs.m_fToleranceProbMax;
    }
    return *this;
}

float MonsterSFContDamageTolerance::ValidateTolerance(float tolerance) const
{
    return std::clamp(tolerance, MIN_TOLERANCE, MAX_TOLERANCE);
}

bool MonsterSFContDamageTolerance::ShouldRecover() const
{
    if (m_dwLastUpdateTime == INVALID_TIME) {
        return false;
    }
    
    uint32_t currentTime = GetCurrentTime();
    uint32_t timeDiff = (currentTime >= m_dwLastUpdateTime) ? 
                        (currentTime - m_dwLastUpdateTime) : 0;
    
    return timeDiff >= RECOVERY_INTERVAL_MS;
}

void MonsterSFContDamageTolerance::ProcessRecovery()
{
    SetSFDamageTolerance_Variation(RECOVERY_RATE);
}

bool MonsterSFContDamageTolerance::IsMonsterValid() const
{
    return m_pMonster != nullptr && m_pMonster->m_bLive;
}

uint32_t MonsterSFContDamageTolerance::GetCurrentTime() const
{
    return GetLoopTime();
}

int MonsterSFContDamageTolerance::GenerateRandomPercent() const
{
    return rand() % 100;
}

// MonsterSFContDamageToleranceUtils implementation

namespace MonsterSFContDamageToleranceUtils {

std::unique_ptr<MonsterSFContDamageTolerance> CreateTolerance()
{
    return std::make_unique<MonsterSFContDamageTolerance>();
}

std::unique_ptr<MonsterSFContDamageTolerance> CreateConfiguredTolerance(
    float maxTolerance, 
    float currentTolerance)
{
    auto tolerance = std::make_unique<MonsterSFContDamageTolerance>();
    tolerance->Init(maxTolerance);
    tolerance->SetToleranceProb(currentTolerance);
    return tolerance;
}

bool ValidateTolerance(const MonsterSFContDamageTolerance* pTolerance)
{
    return pTolerance != nullptr && pTolerance->IsValid();
}

int CalculateTolerancePercentage(const MonsterSFContDamageTolerance* pTolerance)
{
    if (!pTolerance) {
        return 0;
    }
    
    return static_cast<int>(pTolerance->GetToleranceProb() * 100.0f);
}

size_t GetMemoryFootprint(const MonsterSFContDamageTolerance* pTolerance)
{
    if (!pTolerance) {
        return 0;
    }
    
    // Calculate approximate memory usage
    return sizeof(MonsterSFContDamageTolerance);
}

} // namespace MonsterSFContDamageToleranceUtils

// Legacy C-style interface implementation

extern "C" {

void MonsterSFContDamageTolerance_Constructor(MonsterSFContDamageTolerance* pThis)
{
    if (pThis) {
        new (pThis) MonsterSFContDamageTolerance();
    }
}

void MonsterSFContDamageTolerance_Destructor(MonsterSFContDamageTolerance* pThis)
{
    if (pThis) {
        pThis->~MonsterSFContDamageTolerance();
    }
}

void MonsterSFContDamageTolerance_Init(MonsterSFContDamageTolerance* pThis, float maxToleranceValue)
{
    if (pThis) {
        pThis->Init(maxToleranceValue);
    }
}

void MonsterSFContDamageTolerance_OnlyOnceInit(MonsterSFContDamageTolerance* pThis, CMonster* pMonster)
{
    if (pThis) {
        pThis->OnlyOnceInit(pMonster);
    }
}

void MonsterSFContDamageTolerance_Update(MonsterSFContDamageTolerance* pThis)
{
    if (pThis) {
        pThis->Update();
    }
}

bool MonsterSFContDamageTolerance_IsSFContDamage(MonsterSFContDamageTolerance* pThis)
{
    return pThis ? pThis->IsSFContDamage() : false;
}

void MonsterSFContDamageTolerance_SetSFDamageTolerance_Variation(MonsterSFContDamageTolerance* pThis, float addValue)
{
    if (pThis) {
        pThis->SetSFDamageTolerance_Variation(addValue);
    }
}

float MonsterSFContDamageTolerance_GetToleranceProb(MonsterSFContDamageTolerance* pThis)
{
    return pThis ? pThis->GetToleranceProb() : 0.0f;
}

} // extern "C"
