#include "../Headers/MonsterStateData.h"
#include <cstring>
#include <sstream>
#include <iomanip>
#include <cassert>

// MonsterStateData implementation

MonsterStateData::MonsterStateData()
    : m_wSendChunkData(0)
{
}

MonsterStateData::MonsterStateData(const MonsterStateData& other)
    : m_wSendChunkData(other.m_wSendChunkData)
{
}

MonsterStateData::~MonsterStateData()
{
    // No resources to clean up
}

uint16_t MonsterStateData::GetStateChunk() const
{
    return m_wSendChunkData;
}

void MonsterStateData::SetStateChunk(uint16_t stateChunk)
{
    m_wSendChunkData = stateChunk;
    ValidateBits();
}

uint8_t MonsterStateData::GetMoveType() const
{
    return ExtractBits(MOVE_TYPE_MASK, 0);
}

void MonsterStateData::SetMoveType(uint8_t moveType)
{
    SetBits(MOVE_TYPE_MASK, 0, moveType & 0x01);
}

bool MonsterStateData::GetSpeedEffect() const
{
    return (m_wSendChunkData & SPEED_EFFECT_MASK) != 0;
}

void MonsterStateData::SetSpeedEffect(bool hasEffect)
{
    SetBits(SPEED_EFFECT_MASK, 1, hasEffect ? 1 : 0);
}

uint8_t MonsterStateData::GetEmotionState() const
{
    return ExtractBits(EMOTION_STATE_MASK, EMOTION_STATE_SHIFT);
}

void MonsterStateData::SetEmotionState(uint8_t emotionState)
{
    SetBits(EMOTION_STATE_MASK, EMOTION_STATE_SHIFT, emotionState & 0x07);
}

uint8_t MonsterStateData::GetCombatState() const
{
    return ExtractBits(COMBAT_STATE_MASK, COMBAT_STATE_SHIFT);
}

void MonsterStateData::SetCombatState(uint8_t combatState)
{
    SetBits(COMBAT_STATE_MASK, COMBAT_STATE_SHIFT, combatState & 0x01);
}

void MonsterStateData::Clear()
{
    m_wSendChunkData = 0;
}

bool MonsterStateData::operator==(const MonsterStateData& rhs) const
{
    return m_wSendChunkData == rhs.m_wSendChunkData;
}

bool MonsterStateData::operator!=(const MonsterStateData& rhs) const
{
    return m_wSendChunkData != rhs.m_wSendChunkData;
}

MonsterStateData& MonsterStateData::operator=(const MonsterStateData& rhs)
{
    if (this != &rhs) {
        m_wSendChunkData = rhs.m_wSendChunkData;
    }
    return *this;
}

bool MonsterStateData::IsValid() const
{
    // Check if emotion state is within valid range (0-7)
    uint8_t emotionState = GetEmotionState();
    if (emotionState > 7) {
        return false;
    }
    
    // Check if move type is valid (0-1)
    uint8_t moveType = GetMoveType();
    if (moveType > 1) {
        return false;
    }
    
    // Check if combat state is valid (0-1)
    uint8_t combatState = GetCombatState();
    if (combatState > 1) {
        return false;
    }
    
    return true;
}

std::string MonsterStateData::ToString() const
{
    std::stringstream ss;
    ss << "MonsterStateData [0x" << std::hex << std::setw(4) << std::setfill('0') 
       << static_cast<int>(m_wSendChunkData) << "]:" << std::dec << std::endl;
    ss << "  Move Type: " << static_cast<int>(GetMoveType()) << std::endl;
    ss << "  Speed Effect: " << (GetSpeedEffect() ? "Yes" : "No") << std::endl;
    ss << "  Emotion State: " << static_cast<int>(GetEmotionState()) << std::endl;
    ss << "  Combat State: " << static_cast<int>(GetCombatState()) << std::endl;
    return ss.str();
}

void MonsterStateData::ValidateBits()
{
    // Ensure emotion state is within valid range (0-7)
    uint8_t emotionState = GetEmotionState();
    if (emotionState > 7) {
        SetEmotionState(emotionState & 0x07);
    }
    
    // Ensure move type is valid (0-1)
    uint8_t moveType = GetMoveType();
    if (moveType > 1) {
        SetMoveType(moveType & 0x01);
    }
    
    // Ensure combat state is valid (0-1)
    uint8_t combatState = GetCombatState();
    if (combatState > 1) {
        SetCombatState(combatState & 0x01);
    }
}

uint8_t MonsterStateData::ExtractBits(uint16_t mask, uint8_t shift) const
{
    return static_cast<uint8_t>((m_wSendChunkData & mask) >> shift);
}

void MonsterStateData::SetBits(uint16_t mask, uint8_t shift, uint8_t value)
{
    // Clear the bits in the mask
    m_wSendChunkData &= ~mask;
    
    // Set the new value
    m_wSendChunkData |= (static_cast<uint16_t>(value) << shift) & mask;
}

// MonsterStateDataUtils implementation

namespace MonsterStateDataUtils {

std::unique_ptr<MonsterStateData> CreateStateData()
{
    return std::make_unique<MonsterStateData>();
}

std::unique_ptr<MonsterStateData> CreateConfiguredStateData(
    uint8_t moveType, 
    uint8_t emotionState, 
    uint8_t combatState, 
    bool speedEffect)
{
    auto stateData = std::make_unique<MonsterStateData>();
    stateData->SetMoveType(moveType);
    stateData->SetEmotionState(emotionState);
    stateData->SetCombatState(combatState);
    stateData->SetSpeedEffect(speedEffect);
    return stateData;
}

bool ValidateStateData(const MonsterStateData* pStateData)
{
    return pStateData != nullptr && pStateData->IsValid();
}

std::string CompareStates(const MonsterStateData& state1, const MonsterStateData& state2)
{
    if (state1 == state2) {
        return "";
    }
    
    std::stringstream ss;
    ss << "State differences:" << std::endl;
    
    if (state1.GetMoveType() != state2.GetMoveType()) {
        ss << "  Move Type: " << static_cast<int>(state1.GetMoveType()) 
           << " vs " << static_cast<int>(state2.GetMoveType()) << std::endl;
    }
    
    if (state1.GetSpeedEffect() != state2.GetSpeedEffect()) {
        ss << "  Speed Effect: " << (state1.GetSpeedEffect() ? "Yes" : "No")
           << " vs " << (state2.GetSpeedEffect() ? "Yes" : "No") << std::endl;
    }
    
    if (state1.GetEmotionState() != state2.GetEmotionState()) {
        ss << "  Emotion State: " << static_cast<int>(state1.GetEmotionState())
           << " vs " << static_cast<int>(state2.GetEmotionState()) << std::endl;
    }
    
    if (state1.GetCombatState() != state2.GetCombatState()) {
        ss << "  Combat State: " << static_cast<int>(state1.GetCombatState())
           << " vs " << static_cast<int>(state2.GetCombatState()) << std::endl;
    }
    
    return ss.str();
}

} // namespace MonsterStateDataUtils

// Legacy C-style interface implementation

extern "C" {

void MonsterStateData_Constructor(MonsterStateData* pThis)
{
    if (pThis) {
        new (pThis) MonsterStateData();
    }
}

void MonsterStateData_Destructor(MonsterStateData* pThis)
{
    if (pThis) {
        pThis->~MonsterStateData();
    }
}

uint16_t MonsterStateData_GetStateChunk(MonsterStateData* pThis)
{
    return pThis ? pThis->GetStateChunk() : 0;
}

void MonsterStateData_SetStateChunk(MonsterStateData* pThis, uint16_t stateChunk)
{
    if (pThis) {
        pThis->SetStateChunk(stateChunk);
    }
}

uint8_t MonsterStateData_GetMoveType(MonsterStateData* pThis)
{
    return pThis ? pThis->GetMoveType() : 0;
}

void MonsterStateData_SetMoveType(MonsterStateData* pThis, uint8_t moveType)
{
    if (pThis) {
        pThis->SetMoveType(moveType);
    }
}

uint8_t MonsterStateData_GetEmotionState(MonsterStateData* pThis)
{
    return pThis ? pThis->GetEmotionState() : 0;
}

void MonsterStateData_SetEmotionState(MonsterStateData* pThis, uint8_t emotionState)
{
    if (pThis) {
        pThis->SetEmotionState(emotionState);
    }
}

uint8_t MonsterStateData_GetCombatState(MonsterStateData* pThis)
{
    return pThis ? pThis->GetCombatState() : 0;
}

void MonsterStateData_SetCombatState(MonsterStateData* pThis, uint8_t combatState)
{
    if (pThis) {
        pThis->SetCombatState(combatState);
    }
}

bool MonsterStateData_NotEqual(MonsterStateData* pThis, MonsterStateData* pOther)
{
    if (!pThis || !pOther) {
        return false;
    }
    return *pThis != *pOther;
}

} // extern "C"
