#pragma once

/**
 * @file CMoveMapLimitInfo.h
 * @brief Base class for map movement limitation information
 * @details Abstract base class that defines the interface for map movement limitations
 * <AUTHOR> Development Team
 * @date 2024
 */

#include <memory>

// Forward declarations
class CMoveMapLimitRightInfo;
class CItemStore;

/**
 * @enum LimitType
 * @brief Types of movement limitations
 */
enum class LimitType : int
{
    Portal = 0,     ///< Portal-based limitation
    Zone = 1,       ///< Zone-based limitation
    Map = 2,        ///< Map-based limitation
    Invalid = -1    ///< Invalid limitation type
};

/**
 * @class CMoveMapLimitInfo
 * @brief Base class for map movement limitation information
 * 
 * This abstract base class defines the interface for different types of
 * map movement limitations. It provides common functionality for:
 * - Storing limitation type and index information
 * - Comparing limitation criteria
 * - Processing movement requests (virtual method)
 * - Managing store NPC associations
 */
class CMoveMapLimitInfo
{
public:
    // Constructor and destructor
    CMoveMapLimitInfo(unsigned int uiInx, int iType);
    virtual ~CMoveMapLimitInfo();

    // Static factory method
    static CMoveMapLimitInfo* Create(unsigned int uiInx, int iType);

    // Core functionality (virtual methods to be overridden by derived classes)
    virtual char Request(int iRequestType, int iMapIndex, unsigned int dwStoreRecordIndex,
                        int iUserIndex, char* pRequest, CMoveMapLimitRightInfo* pkRight) = 0;

    // Information retrieval
    bool IsEqualLimit(int iType, int iMapIndex, unsigned int dwStoreRecordIndex) const;
    int GetType() const;
    unsigned int GetInx() const;
    int GetMapIndex() const;
    CItemStore* GetStoreNPC() const;

    // Setters
    void SetMapIndex(int iMapIndex);
    void SetStoreNPC(CItemStore* pStoreNPC);

protected:
    // Protected members accessible by derived classes
    unsigned int m_uiInx;           ///< Limitation index
    int m_iMapInx;                  ///< Map index (-1 if not set)
    CItemStore* m_pStoreNPC;        ///< Associated store NPC (can be null)
    LimitType m_eType;              ///< Type of limitation

private:
    // Prevent copying
    CMoveMapLimitInfo(const CMoveMapLimitInfo&) = delete;
    CMoveMapLimitInfo& operator=(const CMoveMapLimitInfo&) = delete;
};
