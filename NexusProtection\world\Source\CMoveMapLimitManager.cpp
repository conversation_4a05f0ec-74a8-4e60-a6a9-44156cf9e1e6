/**
 * @file CMoveMapLimitManager.cpp
 * @brief Implementation of map movement limitation and rights management system
 * <AUTHOR> Development Team
 * @date 2024
 */

#include "../Headers/CMoveMapLimitManager.h"
#include "../Headers/CMoveMapLimitInfoList.h"
#include "../Headers/CMoveMapLimitRightInfoList.h"
#include "../Headers/CMoveMapLimitRightInfo.h"
#include "../../player/Headers/CPlayer.h"
#include "../../system/Headers/CMoveMapLimitEnviromentValues.h"
#include <vector>
#include <new>

// Static member initialization
CMoveMapLimitManager* CMoveMapLimitManager::ms_Instance = nullptr;

/**
 * @brief Private constructor for singleton pattern
 * @details Initializes the right info and limit info lists
 */
CMoveMapLimitManager::CMoveMapLimitManager()
    : m_kRightInfo()
    , m_kLimitInfo()
{
    // Member initialization is handled by member initializer list
}

/**
 * @brief Destructor
 * @details Cleans up the right info and limit info lists
 */
CMoveMapLimitManager::~CMoveMapLimitManager()
{
    // Destructors for m_kRightInfo and m_kLimitInfo are called automatically
}

/**
 * @brief Get singleton instance
 * @return Pointer to the singleton instance
 * @details Creates the instance if it doesn't exist using lazy initialization
 */
CMoveMapLimitManager* CMoveMapLimitManager::Instance()
{
    if (!ms_Instance)
    {
        try
        {
            ms_Instance = new CMoveMapLimitManager();
        }
        catch (const std::bad_alloc&)
        {
            ms_Instance = nullptr;
        }
    }
    return ms_Instance;
}

/**
 * @brief Destroy singleton instance
 * @details Safely deletes the singleton instance and sets pointer to null
 */
void CMoveMapLimitManager::Destroy()
{
    if (ms_Instance)
    {
        delete ms_Instance;
        ms_Instance = nullptr;
    }
}

/**
 * @brief Initialize the movement limitation system
 * @return true if initialization successful, false otherwise
 * @details Initializes environment values and both info lists
 */
bool CMoveMapLimitManager::Init()
{
    // Initialize environment values first
    if (!CMoveMapLimitEnviromentValues::Init())
    {
        return false;
    }

    // Create vector for right type list
    std::vector<int> vecRightTypeList;
    
    try
    {
        // Initialize limit info list
        if (!m_kLimitInfo.Init(vecRightTypeList))
        {
            return false;
        }

        // Initialize right info list
        if (!m_kRightInfo.Init(vecRightTypeList))
        {
            return false;
        }

        return true;
    }
    catch (...)
    {
        return false;
    }
}

/**
 * @brief Load player-specific movement limitation data
 * @param pkPlayer Pointer to the player object
 * @details Loads rights information and limit information for the specified player
 */
void CMoveMapLimitManager::Load(CPlayer* pkPlayer)
{
    if (!pkPlayer)
    {
        return;
    }

    try
    {
        // Load rights information for the player
        m_kRightInfo.Load(pkPlayer);
        
        // Get the player's right info
        CMoveMapLimitRightInfo* pRightInfo = m_kRightInfo.Get(pkPlayer->m_ObjID.m_wIndex);
        
        // Load limit information using the player and their rights
        m_kLimitInfo.Load(pkPlayer, pRightInfo);
    }
    catch (...)
    {
        // Handle any exceptions during loading
        // Log error if logging system is available
    }
}

/**
 * @brief Process movement limitation request
 * @param iLimitType Type of limitation
 * @param iRequestType Type of request
 * @param iMapIndex Map index
 * @param dwStoreRecordIndex Store record index
 * @param iUserIndex User index
 * @param pRequest Request data buffer
 * @return Result of the request processing
 * @details Delegates the request to the limit info list after getting user rights
 */
char CMoveMapLimitManager::Request(int iLimitType, int iRequestType, int iMapIndex,
                                  unsigned int dwStoreRecordIndex, int iUserIndex, char* pRequest)
{
    try
    {
        // Get the user's right information
        CMoveMapLimitRightInfo* pRightInfo = m_kRightInfo.Get(iUserIndex);
        
        // Delegate the request to the limit info list
        return m_kLimitInfo.Request(iLimitType, iRequestType, iMapIndex,
                                   dwStoreRecordIndex, iUserIndex, pRequest, pRightInfo);
    }
    catch (...)
    {
        // Return failure on any exception
        return 0;
    }
}
