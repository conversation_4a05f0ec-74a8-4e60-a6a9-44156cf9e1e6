# CMoveMapLimitRightInfoList

## Overview

The `CMoveMapLimitRightInfoList` class is a container that manages a collection of `CMoveMapLimitRightInfo` objects within the NexusProtection game system. It provides centralized management of user rights and permissions for map movement limitations.

## Purpose

This class serves as a container and manager for:
- **Rights Information Storage**: Storing and organizing CMoveMapLimitRightInfo instances
- **Index-Based Access**: Providing efficient access to rights information by index
- **Container Operations**: Supporting standard container operations like add, remove, clear
- **Search Functionality**: Finding rights information by type or other criteria
- **Iterator Support**: Enabling STL-compatible iteration over rights objects

## Architecture

### Container Design
The class uses a `std::vector<CMoveMapLimitRightInfo>` to store rights information objects, providing efficient access, iteration, and memory management.

### Key Components
- **CMoveMapLimitRightInfo**: Individual rights information objects
- **STL Vector**: Underlying container for efficient storage and access
- **Iterator Support**: Full STL-compatible iterator interface

## Class Interface

### Core Methods
- `Get(iIndex)`: Retrieves rights information by index (both const and non-const versions)
- `Add(rightInfo)`: Adds a rights information object to the container
- `Remove(iIndex)`: Removes rights information by index
- `Clear()`: Removes all rights information objects

### Container Operations
- `Size()`: Returns the number of rights information objects
- `Empty()`: Checks if the container is empty
- `IsValidIndex(iIndex)`: Validates index bounds
- `Reserve(capacity)`: Reserves memory for improved performance
- `Capacity()`: Returns current container capacity

### Iterator Support
- `begin()`, `end()`: Standard iterator interface (both const and non-const)
- Full STL compatibility for range-based loops and algorithms

### Search Operations
- `Find(iRightType)`: Searches for rights information by type (both const and non-const versions)

## Usage Example

```cpp
// Create a rights information list
CMoveMapLimitRightInfoList rightsList;

// Reserve capacity for performance
rightsList.Reserve(20);

// Add rights information objects
CMoveMapLimitRightInfo portalRight(1, "Portal Access");
CMoveMapLimitRightInfo zoneRight(2, "Zone Access");

rightsList.Add(portalRight);
rightsList.Add(zoneRight);

// Access by index
CMoveMapLimitRightInfo* pRight = rightsList.Get(0);
if (pRight)
{
    // Use the rights information
    int rightType = pRight->GetType();
}

// Search by type
CMoveMapLimitRightInfo* pFoundRight = rightsList.Find(1);
if (pFoundRight)
{
    // Found portal access right
}

// Iterate through all rights
for (const auto& rightInfo : rightsList)
{
    // Process each rights information object
    int type = rightInfo.GetType();
}

// Check container status
if (!rightsList.Empty())
{
    size_t count = rightsList.Size();
    // Process based on count
}

// Remove specific rights
if (rightsList.IsValidIndex(1))
{
    rightsList.Remove(1);
}

// Clear all rights
rightsList.Clear();
```

## Container Operations

### Adding Elements
- `Add(rightInfo)`: Adds a copy of the rights information object
- Automatic memory management with exception safety
- Capacity automatically grows as needed

### Removing Elements
- `Remove(iIndex)`: Removes element at specific index
- `Clear()`: Removes all elements
- Proper bounds checking and error handling

### Accessing Elements
- `Get(iIndex)`: Safe index-based access with bounds checking
- Returns nullptr for invalid indices
- Both const and non-const versions available

## Dependencies

### Headers Required
- `CMoveMapLimitRightInfo.h` (for rights information objects)
- Standard C++ containers (`<vector>`, `<algorithm>`)

### Related Classes
- **CMoveMapLimitRightInfo**: Individual rights information objects
- **CMoveMapLimitManager**: Parent manager class
- **CMoveMapLimitInfoList**: Related limitation information container

## Implementation Notes

### Memory Management
- Uses RAII principles for automatic cleanup
- Exception-safe operations with proper error handling
- Efficient memory usage with vector-based storage

### Performance Considerations
- Vector container provides O(1) random access
- Reserve method available for performance optimization
- Iterator support for efficient traversal
- Capacity management for memory efficiency

### Error Handling
- Comprehensive bounds checking for all index operations
- Exception safety in all public methods
- Graceful handling of memory allocation failures
- Null pointer returns for invalid operations

### STL Compatibility
- Full iterator interface for STL algorithm compatibility
- Range-based loop support
- Standard container semantics

## Refactoring Notes

This class was refactored from decompiled C source files to modern C++17/C++20 standards:

### Original Files Consolidated
- `0CMoveMapLimitRightInfoListQEAAXZ_1403A1E10.c` (Constructor)
- `1CMoveMapLimitRightInfoListQEAAXZ_1403A1E60.c` (Destructor)
- `GetCMoveMapLimitRightInfoListQEAAPEAVCMoveMapLimit_1403A1FE0.c` (Get method)

### Modernization Changes
- Converted from C-style to modern C++ container class design
- Added comprehensive STL container interface
- Implemented exception safety and RAII principles
- Added bounds checking and error handling
- Enhanced with search and utility methods
- Added const-correctness throughout the interface
- Implemented iterator support for STL compatibility

## Testing Recommendations

1. **Unit Tests**: Test all container operations (add, remove, get, clear)
2. **Bounds Tests**: Verify proper bounds checking and error handling
3. **Iterator Tests**: Test iterator functionality and STL compatibility
4. **Performance Tests**: Validate efficiency of operations and memory usage
5. **Exception Tests**: Verify graceful handling of error conditions
6. **Integration Tests**: Test interaction with CMoveMapLimitRightInfo objects

## Future Enhancements

- Consider adding sorting capabilities for rights information
- Implement bulk operations for multiple rights management
- Add serialization support for persistent storage
- Consider thread safety if multi-threading is required
- Add performance monitoring and metrics
- Implement custom allocators if needed for memory optimization

## Related Documentation

- [CMoveMapLimitRightInfo.md](CMoveMapLimitRightInfo.md) - Individual rights objects
- [CMoveMapLimitManager.md](CMoveMapLimitManager.md) - Main manager class
- [CMoveMapLimitInfoList.md](CMoveMapLimitInfoList.md) - Related limitation container
- [CMoveMapLimitRight.md](CMoveMapLimitRight.md) - Base rights class
