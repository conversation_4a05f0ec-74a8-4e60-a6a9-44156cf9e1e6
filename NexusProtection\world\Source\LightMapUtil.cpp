/**
 * @file LightMapUtil.cpp
 * @brief Implementation of light mapping utility functions
 * 
 * This file contains the implementation of light mapping utility functions for
 * texture operations, color sampling, and rendering support.
 * 
 * @note Refactored from decompiled light map utility functions
 * Original functions:
 * - GetLightMapColor (Address: 0x140502530)
 * - GetLightMapSurface (Address: 0x1405025F0)
 * - GetLightMapTexSize (Address: 0x140500900)
 * - SetLightMap (Address: 0x1404EDF30)
 * - LoadLightMap (Address: 0x1405023A0)
 * - ReleaseLightMap (Address: 0x140502480)
 * - LightMappingTex1 (Address: 0x1404EFAF0)
 * - UnLightMappingTex1 (Address: 0x1404EFB90)
 * - DrawLightMapGroup (Address: 0x1404F1590)
 */

#include "LightMapUtil.h"
#include <cmath>
#include <algorithm>
#include <stdexcept>
#include <cstring>
#include <iostream>

// Global variables (originally from decompiled code)
namespace {
    // Light map data storage
    void* stLightmap = nullptr;           // Originally: stLightmap
    void* LightmapTexID = nullptr;        // Originally: LightmapTexID
    int dword_184A79D88 = 0;              // Light map count
    uint64_t qword_184A79DA0 = 0;         // Light map texture info
    uint32_t dword_184A79C54 = 256;       // Default texture size
    bool lightMappingEnabled = false;     // Light mapping state
}

// External function declarations (would be implemented elsewhere)
extern "C" {
    IDirect3DTexture8* R3GetSurface(uint32_t textureId);
    void* R3GetTexInfoR3T(const char* filename, int flags);
    void* LoadR3TLightMap(struct R3Texture* texture, int format);
    void* Dmalloc(size_t size);
    void Dfree(void* ptr);
    IDirect3DDevice8* GetD3dDevice();
    void GetMainMaterial();
    void MultiTexOff();
    void MultiTexOn();
    void BlendOn(int mode);
    void BlendOff();
    int strcmp_0(const char* str1, const char* str2);
    float ffloor(float value);
}

// LightMapData implementation

LightMapData::~LightMapData() {
    if (pixelData) {
        Dfree(pixelData);
        pixelData = nullptr;
    }
}

LightMapData::LightMapData(LightMapData&& other) noexcept
    : width(other.width), height(other.height), pixelData(other.pixelData) {
    other.width = 0;
    other.height = 0;
    other.pixelData = nullptr;
}

LightMapData& LightMapData::operator=(LightMapData&& other) noexcept {
    if (this != &other) {
        if (pixelData) {
            Dfree(pixelData);
        }
        width = other.width;
        height = other.height;
        pixelData = other.pixelData;
        
        other.width = 0;
        other.height = 0;
        other.pixelData = nullptr;
    }
    return *this;
}

// LightMapColor implementation

void LightMapColor::Clamp() {
    u = std::clamp(u, 0.0f, 1.0f);
    v = std::clamp(v, 0.0f, 1.0f);
}

bool LightMapColor::IsValid() const {
    return u >= 0.0f && u <= 1.0f && v >= 0.0f && v <= 1.0f;
}

// LightMapUtil namespace implementation

namespace LightMapUtil {

uint32_t GetLightMapColor(float* uvCoords, int lightMapIndex) {
    if (!uvCoords) {
        return Constants::INVALID_COLOR;
    }
    
    // Clamp UV coordinates to valid range
    float u = uvCoords[0];
    float v = uvCoords[1];
    
    // Clamp to [0.0, 1.0] range (original behavior)
    if (u <= 0.0f) u = 0.0f;
    if (v <= 0.0f) v = 0.0f;
    if (u >= 1.0f) u = Constants::FLOAT_1_0;
    if (v >= 1.0f) v = Constants::FLOAT_1_0;
    
    // Update the input coordinates (original behavior)
    uvCoords[0] = u;
    uvCoords[1] = v;
    
    // Check if light mapping is available
    if (dword_184A79D88 == 0 || !stLightmap) {
        return Constants::INVALID_COLOR;
    }
    
    // Validate light map index
    if (!IsValidLightMapIndex(lightMapIndex)) {
        return Constants::INVALID_COLOR;
    }
    
    try {
        // Get light map data (simplified from original complex pointer arithmetic)
        uint64_t* lightMapArray = static_cast<uint64_t*>(stLightmap);
        uint64_t lightMapData = lightMapArray[lightMapIndex];
        
        // Extract width and height from light map data
        uint16_t width = *reinterpret_cast<uint16_t*>(lightMapData);
        uint16_t height = *reinterpret_cast<uint16_t*>(lightMapData + 2);
        uint16_t* pixelData = *reinterpret_cast<uint16_t**>(lightMapData + 8);
        
        if (!pixelData || width == 0 || height == 0) {
            return Constants::INVALID_COLOR;
        }
        
        // Calculate pixel coordinates
        int pixelX = static_cast<int>(std::floor(width * u));
        int pixelY = static_cast<int>(std::floor(height * v));
        
        // Ensure coordinates are within bounds
        pixelX = std::clamp(pixelX, 0, static_cast<int>(width) - 1);
        pixelY = std::clamp(pixelY, 0, static_cast<int>(height) - 1);
        
        // Get pixel value (RGB565 format)
        uint16_t pixelValue = pixelData[pixelY * width + pixelX];
        
        // Convert RGB565 to ARGB8888
        return ConvertRGB565ToARGB8888(pixelValue);
    }
    catch (...) {
        return Constants::INVALID_COLOR;
    }
}

uint32_t GetLightMapColor(const LightMapColor& color, int lightMapIndex) {
    float uvCoords[2] = { color.u, color.v };
    return GetLightMapColor(uvCoords, lightMapIndex);
}

IDirect3DTexture8* GetLightMapSurface(int lightMapIndex) {
    if (!LightmapTexID || !IsValidLightMapIndex(lightMapIndex)) {
        return nullptr;
    }
    
    try {
        uint32_t* textureIds = static_cast<uint32_t*>(LightmapTexID);
        uint32_t textureId = textureIds[lightMapIndex];
        return R3GetSurface(textureId);
    }
    catch (...) {
        return nullptr;
    }
}

uint32_t GetLightMapTexSize() {
    return dword_184A79C54;
}

void SetLightMap(int lightMapIndex) {
    IDirect3DDevice8* device = GetD3dDevice();
    if (!device) {
        return;
    }
    
    try {
        if (lightMapIndex == Constants::INVALID_TEXTURE_ID) {
            // Disable light mapping
            // Original: ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, _QWORD))v2->vfptr[20].AddRef)(v2, 1i64, 0i64);
            // This would call SetTexture(1, nullptr) in DirectX
            lightMappingEnabled = false;
        } else {
            // Enable light mapping with specified texture
            IDirect3DTexture8* surface = GetLightMapSurface(lightMapIndex);
            if (surface) {
                // Original: ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, void *))v4[20].AddRef)(v3, 1i64, v5);
                // This would call SetTexture(1, surface) in DirectX
                lightMappingEnabled = true;
            }
        }
    }
    catch (...) {
        lightMappingEnabled = false;
    }
}

bool LoadLightMap(const char* filename) {
    if (!filename) {
        return false;
    }
    
    try {
        // Release existing light maps
        ReleaseLightMap();
        
        // Load texture info
        qword_184A79DA0 = reinterpret_cast<uint64_t>(R3GetTexInfoR3T(filename, 0));
        if (!qword_184A79DA0) {
            return false;
        }
        
        // Load light map data
        stLightmap = LoadR3TLightMap(reinterpret_cast<struct R3Texture*>(qword_184A79DA0), 23); // D3DFMT_R5G6B5 = 23
        if (!stLightmap) {
            return false;
        }
        
        // Get light map count
        dword_184A79D88 = *reinterpret_cast<int*>(qword_184A79DA0 + 132);
        
        // Allocate texture ID array
        LightmapTexID = Dmalloc(4 * dword_184A79D88);
        if (!LightmapTexID) {
            ReleaseLightMap();
            return false;
        }
        
        // Initialize texture IDs
        uint32_t* textureIds = static_cast<uint32_t*>(LightmapTexID);
        uint32_t baseTextureId = *reinterpret_cast<uint32_t*>(qword_184A79DA0 + 128);
        
        for (int i = 0; i < dword_184A79D88; ++i) {
            textureIds[i] = baseTextureId + i;
        }
        
        return true;
    }
    catch (...) {
        ReleaseLightMap();
        return false;
    }
}

void ReleaseLightMap() {
    // Release texture IDs
    if (LightmapTexID) {
        Dfree(LightmapTexID);
        LightmapTexID = nullptr;
        qword_184A79DA0 = 0;
    }
    
    // Release light map data
    if (stLightmap) {
        // Free individual light map entries
        if (dword_184A79D88 > 0) {
            uint64_t* lightMapArray = static_cast<uint64_t*>(stLightmap);
            for (int i = 0; i < dword_184A79D88; ++i) {
                uint64_t lightMapData = lightMapArray[i];
                if (lightMapData) {
                    // Free pixel data
                    void* pixelData = *reinterpret_cast<void**>(lightMapData + 8);
                    if (pixelData) {
                        Dfree(pixelData);
                    }
                    // Free light map structure
                    Dfree(reinterpret_cast<void*>(lightMapData));
                }
            }
        }
        
        dword_184A79D88 = 0;
        Dfree(stLightmap);
        stLightmap = nullptr;
    }
    
    lightMappingEnabled = false;
}

void EnableLightMappingTex(struct _BSP_MAT_GROUP* materialGroup) {
    if (!materialGroup) {
        return;
    }
    
    try {
        IDirect3DDevice8* device = GetD3dDevice();
        if (!device) {
            return;
        }
        
        GetMainMaterial();
        MultiTexOff();
        BlendOn(10);
        
        // Get light map ID from material group (assuming it has a LgtId field)
        int16_t lightId = *reinterpret_cast<int16_t*>(reinterpret_cast<char*>(materialGroup) + offsetof(struct _BSP_MAT_GROUP, LgtId));
        
        if (lightId == -1) {
            // Disable texture
            // Original: ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, _QWORD))v2->vfptr[20].AddRef)(v2, 0i64, 0i64);
        } else {
            // Set light map texture
            IDirect3DTexture8* surface = GetLightMapSurface(lightId);
            if (surface) {
                // Original: ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, void *))v4[20].AddRef)(v2, 0i64, v5);
            }
        }
    }
    catch (...) {
        // Error handling
    }
}

void DisableLightMappingTex() {
    try {
        IDirect3DDevice8* device = GetD3dDevice();
        if (!device) {
            return;
        }
        
        BlendOff();
        // Original DirectX calls would go here
        MultiTexOn();
        lightMappingEnabled = false;
    }
    catch (...) {
        // Error handling
    }
}

void DrawLightMapGroup(struct CVertexBuffer* vertexBuffer, struct _BSP_MAT_GROUP* materialGroup) {
    if (!vertexBuffer || !materialGroup) {
        return;
    }
    
    try {
        IDirect3DDevice8* device = GetD3dDevice();
        if (!device) {
            return;
        }
        
        // Implementation would depend on the specific structure of _BSP_MAT_GROUP
        // and CVertexBuffer, which would need to be defined elsewhere
        
        // Original implementation had complex DirectX rendering calls
        // This is a simplified placeholder
    }
    catch (...) {
        // Error handling
    }
}

// Utility functions

bool IsValidLightMapIndex(int lightMapIndex) {
    return lightMapIndex >= 0 && lightMapIndex < dword_184A79D88;
}

void ClampUVCoordinates(float& u, float& v) {
    u = std::clamp(u, 0.0f, 1.0f);
    v = std::clamp(v, 0.0f, 1.0f);
}

uint32_t ConvertRGB565ToARGB8888(uint16_t rgb565) {
    // Extract RGB components from RGB565
    uint32_t r = (rgb565 >> 11) & 0x1F;  // 5 bits
    uint32_t g = (rgb565 >> 5) & 0x3F;   // 6 bits
    uint32_t b = rgb565 & 0x1F;          // 5 bits
    
    // Convert to 8-bit components
    r = (r << 3) | (r >> 2);  // 5->8 bit
    g = (g << 2) | (g >> 4);  // 6->8 bit
    b = (b << 3) | (b >> 2);  // 5->8 bit
    
    // Combine into ARGB8888 format
    return Constants::DEFAULT_ALPHA | (r << 16) | (g << 8) | b;
}

uint16_t ConvertARGB8888ToRGB565(uint32_t argb8888) {
    uint32_t r = (argb8888 >> 16) & 0xFF;
    uint32_t g = (argb8888 >> 8) & 0xFF;
    uint32_t b = argb8888 & 0xFF;
    
    // Convert to RGB565
    return static_cast<uint16_t>(((r >> 3) << 11) | ((g >> 2) << 5) | (b >> 3));
}

int GetLightMapCount() {
    return dword_184A79D88;
}

bool IsLightMappingEnabled() {
    return lightMappingEnabled;
}

LightMapTexture GetLightMapInfo(int lightMapIndex) {
    LightMapTexture info;
    
    if (IsValidLightMapIndex(lightMapIndex)) {
        info.textureId = lightMapIndex;
        info.width = static_cast<uint16_t>(GetLightMapTexSize());
        info.height = static_cast<uint16_t>(GetLightMapTexSize());
        info.d3dTexture = GetLightMapSurface(lightMapIndex);
    }
    
    return info;
}

bool ValidateLightMapData(int lightMapIndex) {
    if (!IsValidLightMapIndex(lightMapIndex) || !stLightmap) {
        return false;
    }
    
    try {
        uint64_t* lightMapArray = static_cast<uint64_t*>(stLightmap);
        uint64_t lightMapData = lightMapArray[lightMapIndex];
        
        if (!lightMapData) {
            return false;
        }
        
        uint16_t width = *reinterpret_cast<uint16_t*>(lightMapData);
        uint16_t height = *reinterpret_cast<uint16_t*>(lightMapData + 2);
        void* pixelData = *reinterpret_cast<void**>(lightMapData + 8);
        
        return width > 0 && height > 0 && pixelData != nullptr;
    }
    catch (...) {
        return false;
    }
}

} // namespace LightMapUtil

// Legacy C-style interface implementation

extern "C" {

uint64_t GetLightMapColor(float* uvCoords, int lightMapIndex) {
    return static_cast<uint64_t>(LightMapUtil::GetLightMapColor(uvCoords, lightMapIndex));
}

IDirect3DTexture8* GetLightMapSurface(int lightMapIndex) {
    return LightMapUtil::GetLightMapSurface(lightMapIndex);
}

uint64_t GetLightMapTexSize() {
    return static_cast<uint64_t>(LightMapUtil::GetLightMapTexSize());
}

void SetLightMap(int lightMapIndex) {
    LightMapUtil::SetLightMap(lightMapIndex);
}

void LoadLightMap(char* filename) {
    if (filename) {
        LightMapUtil::LoadLightMap(filename);
    }
}

void ReleaseLightMap() {
    LightMapUtil::ReleaseLightMap();
}

void LightMappingTex1(struct _BSP_MAT_GROUP* materialGroup) {
    LightMapUtil::EnableLightMappingTex(materialGroup);
}

void UnLightMappingTex1() {
    LightMapUtil::DisableLightMappingTex();
}

void DrawLightMapGroup(struct CVertexBuffer* vertexBuffer,
                       struct _BSP_MAT_GROUP* materialGroup) {
    LightMapUtil::DrawLightMapGroup(vertexBuffer, materialGroup);
}

} // extern "C"
