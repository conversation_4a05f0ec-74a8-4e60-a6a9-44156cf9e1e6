# CMoveMapLimitManager

## Overview

The `CMoveMapLimitManager` class is a singleton that manages map movement limitations and user rights within the NexusProtection game system. It provides centralized control over player movement restrictions, portal access, and zone limitations.

## Purpose

This class serves as the main coordinator for:
- **User Movement Rights**: Managing player permissions for map access
- **Map Access Restrictions**: Controlling which maps players can enter
- **Portal and Zone Limitations**: Managing portal usage and zone access
- **Player-Specific Movement Controls**: Handling individual player movement restrictions

## Architecture

### Singleton Pattern
The class implements the singleton pattern to ensure only one instance manages the movement limitation system throughout the application lifecycle.

### Key Components
- **CMoveMapLimitRightInfoList**: Manages user rights and permissions
- **CMoveMapLimitInfoList**: Handles map limitation information
- **CMoveMapLimitEnviromentValues**: Environment configuration values

## Class Interface

### Static Methods
- `Instance()`: Returns the singleton instance
- `Destroy()`: Safely destroys the singleton instance

### Core Methods
- `Init()`: Initializes the movement limitation system
- `Load(CPlayer*)`: Loads player-specific movement data
- `Request(...)`: Processes movement limitation requests

## Usage Example

```cpp
// Get the manager instance
CMoveMapLimitManager* pManager = CMoveMapLimitManager::Instance();

// Initialize the system
if (pManager && pManager->Init())
{
    // Load player data
    pManager->Load(pPlayer);
    
    // Process a movement request
    char result = pManager->Request(limitType, requestType, mapIndex, 
                                   storeRecordIndex, userIndex, pRequestData);
}

// Clean up when shutting down
CMoveMapLimitManager::Destroy();
```

## Dependencies

### Headers Required
- `CMoveMapLimitInfoList.h`
- `CMoveMapLimitRightInfoList.h`
- `CMoveMapLimitRightInfo.h`
- `CPlayer.h`
- `CMoveMapLimitEnviromentValues.h`

### Related Classes
- **CPlayer**: Player object containing user information
- **CMoveMapLimitInfoList**: List of map limitations
- **CMoveMapLimitRightInfoList**: List of user rights
- **CMoveMapLimitRightInfo**: Individual user rights information

## Implementation Notes

### Memory Management
- Uses modern C++ memory management practices
- Implements proper exception handling
- Follows RAII principles for resource management

### Thread Safety
- Singleton implementation is not thread-safe by design
- Should be initialized and used from the main game thread

### Error Handling
- Returns appropriate error codes for failed operations
- Uses exception handling internally with graceful fallbacks
- Validates input parameters before processing

## Refactoring Notes

This class was refactored from decompiled C source files to modern C++17/C++20 standards:

### Original Files Consolidated
- `0CMoveMapLimitManagerQEAAXZ_1403A1D10.c` (Constructor)
- `1CMoveMapLimitManagerQEAAXZ_1403A1F10.c` (Destructor)
- `InstanceCMoveMapLimitManagerSAPEAV1XZ_1403A15F0.c` (Instance method)
- `DestroyCMoveMapLimitManagerSAXXZ_1403A16B0.c` (Destroy method)
- `InitCMoveMapLimitManagerQEAA_NXZ_1403A1720.c` (Init method)
- `LoadCMoveMapLimitManagerQEAAXPEAVCPlayerZ_1403A1830.c` (Load method)
- `RequestCMoveMapLimitManagerQEAAEHHHKHPEADZ_1403A19D0.c` (Request method)

### Modernization Changes
- Converted from C-style to modern C++ class design
- Added proper header guards and documentation
- Implemented exception safety and RAII
- Used modern STL containers and smart pointers where appropriate
- Added comprehensive error handling
- Improved code readability and maintainability

## Testing Recommendations

1. **Unit Tests**: Test singleton behavior, initialization, and core methods
2. **Integration Tests**: Test interaction with related classes
3. **Load Tests**: Verify performance with multiple concurrent requests
4. **Error Tests**: Validate error handling and edge cases

## Future Enhancements

- Consider adding thread safety if multi-threading is required
- Implement logging for debugging and monitoring
- Add configuration file support for runtime parameter changes
- Consider adding metrics and performance monitoring
