#include "../Headers/CCircleZone.h"
#include <cstring>
#include <cmath>
#include <algorithm>
#include <cassert>

// Include necessary headers for dependencies
// These would be replaced with actual headers in the real implementation
class CCharacter {
public:
    <PERSON>haracter();
    virtual ~CCharacter();
    void Destroy();
    
protected:
    float m_fCurPos[3];  // Current position
};

class CMapData {
public:
    // Map data implementation
};

class CGameObject {
public:
    void CircleReport(void* type, void* msg, int size, int flags);
};

struct CGameObjectVtbl {
    // Virtual function table
};

// Global utility function (would be in a math utility header)
extern float GetSqrt(const float* pos1, const float* pos2);

// CCircleZone implementation

CCircleZone::CCircleZone()
    : CCharacter()
    , m_state(ZoneState::INACTIVE)
    , m_portalIndex(INVALID_PORTAL)
    , m_color(INVALID_COLOR)
    , m_goalPosition(nullptr)
    , m_isInitialized(false)
{
    InitializeDefaults();
}

CCircleZone::~CCircleZone()
{
    CleanupResources();
    // Parent destructor will be called automatically
}

void CCircleZone::InitializeDefaults()
{
    m_state = ZoneState::INACTIVE;
    m_portalIndex = INVALID_PORTAL;
    m_color = INVALID_COLOR;
    m_goalPosition = nullptr;
    m_isInitialized = false;
    
    // Initialize position array
    std::fill(std::begin(m_currentPosition), std::end(m_currentPosition), 0.0f);
}

bool CCircleZone::Init(uint32_t mapIndex, int playerIndex, int nth, uint16_t index, CMapData* pMap)
{
    if (!ValidateInitParams(pMap)) {
        return false;
    }
    
    // Initialize base character class
    // In the original code, this would call CCharacter::Init with appropriate parameters
    
    // Set up zone-specific properties
    m_portalIndex = static_cast<int>(index);
    m_state = ZoneState::ACTIVE;
    
    // Allocate goal position if needed
    if (!AllocateGoalPosition()) {
        return false;
    }
    
    m_isInitialized = true;
    return true;
}

bool CCircleZone::Create(CMapData* pMap, uint8_t color)
{
    if (!pMap || !CCircleZoneUtils::IsValidColor(color)) {
        return false;
    }
    
    if (m_state == ZoneState::GOAL_REACHED) {
        return false;
    }
    
    m_color = color;
    m_state = ZoneState::ACTIVE;
    
    // Send creation message to network
    SendMsgCreate();
    
    return true;
}

void CCircleZone::Destroy()
{
    if (m_state != ZoneState::INACTIVE) {
        m_state = ZoneState::DESTROYED;
        m_color = INVALID_COLOR;
        
        // Call parent destroy method
        CCharacter::Destroy();
        
        CleanupResources();
    }
}

uint8_t CCircleZone::Goal(CMapData* pMap, float* currentPos)
{
    if (!pMap || m_state == ZoneState::GOAL_REACHED) {
        return 110; // Error code from original implementation
    }
    
    if (IsNearPosition(currentPos)) {
        SendMsgGoal();
        return 0; // Success
    }
    
    return 134; // Not near position error (original: -122 as unsigned char = 134)
}

bool CCircleZone::IsNearPosition(const float* position) const
{
    if (!position) {
        return false;
    }
    
    float distance = GetDistanceToPosition(position);
    return distance <= PROXIMITY_THRESHOLD;
}

float CCircleZone::GetDistanceToPosition(const float* position) const
{
    if (!position) {
        return std::numeric_limits<float>::max();
    }
    
    return CalculateDistance(m_currentPosition, position);
}

void CCircleZone::SetGoalPosition(const float* position)
{
    if (!position) {
        return;
    }
    
    if (!m_goalPosition) {
        AllocateGoalPosition();
    }
    
    if (m_goalPosition) {
        std::memcpy(m_goalPosition, position, 3 * sizeof(float));
    }
}

bool CCircleZone::IsValid() const
{
    return m_isInitialized && 
           m_state != ZoneState::INACTIVE &&
           m_portalIndex != INVALID_PORTAL;
}

void CCircleZone::SendMsgCreate()
{
    // Original implementation sends a network message
    // Message format: [portal_index][color][type=3][subtype=53]
    struct {
        int portalIndex;
        uint8_t color;
        uint8_t type;
        uint8_t subtype;
    } message = {
        m_portalIndex,
        m_color,
        3,    // Type from original
        53    // Subtype from original
    };
    
    // Send via game object's circle report system
    CGameObject* gameObj = reinterpret_cast<CGameObject*>(this);
    gameObj->CircleReport(&message.type, &message, 5, 0);
}

void CCircleZone::SendMsgGoal()
{
    // Original implementation sends goal achievement message
    // Message format: [portal_index][type=4][subtype=174]
    struct {
        int portalIndex;
        uint8_t type;
        uint8_t subtype;
    } message = {
        m_portalIndex,
        4,    // Type from original
        174   // Subtype from original (256 - 82 = 174)
    };
    
    // Send via game object's circle report system
    CGameObject* gameObj = reinterpret_cast<CGameObject*>(this);
    gameObj->CircleReport(&message.type, &message, 4, 0);
}

bool CCircleZone::ValidateInitParams(CMapData* pMap) const
{
    return pMap != nullptr;
}

void CCircleZone::CleanupResources()
{
    DeallocateGoalPosition();
    m_isInitialized = false;
}

bool CCircleZone::AllocateGoalPosition()
{
    if (!m_goalPosition) {
        m_goalPosition = new(std::nothrow) float[3];
        if (m_goalPosition) {
            std::fill(m_goalPosition, m_goalPosition + 3, 0.0f);
            return true;
        }
    }
    return m_goalPosition != nullptr;
}

void CCircleZone::DeallocateGoalPosition()
{
    delete[] m_goalPosition;
    m_goalPosition = nullptr;
}

float CCircleZone::CalculateDistance(const float* pos1, const float* pos2) const
{
    if (!pos1 || !pos2) {
        return std::numeric_limits<float>::max();
    }
    
    float dx = pos1[0] - pos2[0];
    float dy = pos1[1] - pos2[1];
    float dz = pos1[2] - pos2[2];
    
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

// CCircleZoneUtils implementation

namespace CCircleZoneUtils {

std::unique_ptr<CCircleZone> CreateCircleZone()
{
    return std::make_unique<CCircleZone>();
}

bool ValidateCircleZone(const CCircleZone* pZone)
{
    return pZone != nullptr && pZone->IsValid();
}

float CalculateSquaredDistance(const float* pos1, const float* pos2)
{
    if (!pos1 || !pos2) {
        return std::numeric_limits<float>::max();
    }
    
    float dx = pos1[0] - pos2[0];
    float dy = pos1[1] - pos2[1];
    float dz = pos1[2] - pos2[2];
    
    return dx * dx + dy * dy + dz * dz;
}

bool IsValidColor(uint8_t color)
{
    return color != CCircleZone::INVALID_COLOR;
}

} // namespace CCircleZoneUtils

// Legacy C-style interface implementation

extern "C" {

void CCircleZone_Constructor(CCircleZone* pThis)
{
    if (pThis) {
        new (pThis) CCircleZone();
    }
}

void CCircleZone_Destructor(CCircleZone* pThis)
{
    if (pThis) {
        pThis->~CCircleZone();
    }
}

bool CCircleZone_Create(CCircleZone* pThis, CMapData* pMap, uint8_t color)
{
    return pThis ? pThis->Create(pMap, color) : false;
}

void CCircleZone_Destroy(CCircleZone* pThis)
{
    if (pThis) {
        pThis->Destroy();
    }
}

uint8_t CCircleZone_Goal(CCircleZone* pThis, CMapData* pMap, float* currentPos)
{
    return pThis ? pThis->Goal(pMap, currentPos) : 255;
}

uint8_t CCircleZone_GetColor(CCircleZone* pThis)
{
    return pThis ? pThis->GetColor() : CCircleZone::INVALID_COLOR;
}

int CCircleZone_GetPortalIndex(CCircleZone* pThis)
{
    return pThis ? pThis->GetPortalIndex() : CCircleZone::INVALID_PORTAL;
}

bool CCircleZone_IsNearPosition(CCircleZone* pThis, const float* position)
{
    return pThis ? pThis->IsNearPosition(position) : false;
}

} // extern "C"
