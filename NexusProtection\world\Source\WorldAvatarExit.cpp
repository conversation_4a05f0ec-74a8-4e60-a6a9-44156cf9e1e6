/*
 * WorldAvatarExit.cpp - World Avatar Exit System Implementation
 * Refactored for Visual Studio 2022 compatibility
 * Original: wa_ExitWorldYAXPEAU_CLIDZ_140046190.c
 */

#include "../Headers/WorldAvatarExit.h"
#include <iostream>
#include <cstring>
#include <algorithm>
#include <cassert>

// External dependencies (these would need to be properly defined)
extern CPartyPlayer g_PartyPlayer[2532];  // Global party player array
extern CPlayer g_Player[];                // Global player array

// External function implementations (placeholders for actual game functions)
namespace ExternalFunctions {
    PartyExitFunction CPartyPlayer_ExitWorld = nullptr;
    PartyModeCheckFunction CPartyPlayer_IsPartyMode = nullptr;
    PartyMemberGetFunction CPartyPlayer_GetPtrPartyMember = nullptr;
    PlayerMessageFunction CPlayer_SendMsg_PartyLeaveSelfResult = nullptr;
    PlayerMessageFunction CPlayer_SendMsg_PartySuccessResult = nullptr;
}

/**
 * ExitResult Implementation
 */
const char* ExitResult::GetStatusString() const {
    switch (status) {
        case Status::Success: return "Success";
        case Status::InvalidClient: return "Invalid Client";
        case Status::NotLoggedIn: return "Not Logged In";
        case Status::SerialMismatch: return "Serial Mismatch";
        case Status::PartyUpdateFailed: return "Party Update Failed";
        case Status::NotificationFailed: return "Notification Failed";
        case Status::SystemError: return "System Error";
        default: return "Unknown";
    }
}

/**
 * WorldAvatarExitManager Implementation
 */
WorldAvatarExitManager::WorldAvatarExitManager() {
    // Constructor implementation
}

WorldAvatarExitManager::~WorldAvatarExitManager() {
    // Destructor implementation
}

/**
 * Main exit function - refactored from original wa_ExitWorld
 */
ExitResult WorldAvatarExitManager::ExitWorld(const ClientID* pClientID) {
    // Input validation
    if (!ValidateClientExit(pClientID)) {
        ExitResult result(ExitResult::Status::InvalidClient, "Invalid client ID provided");
        LogExitAttempt(pClientID, result);
        return result;
    }

    try {
        // Get the party player instance (equivalent to original array access)
        CPartyPlayer* pLeavingPlayer = GetPartyPlayer(pClientID->wIndex);
        if (!pLeavingPlayer) {
            ExitResult result(ExitResult::Status::SystemError, "Failed to retrieve party player");
            LogExitAttempt(pClientID, result);
            return result;
        }

        // Validate player state (equivalent to original serial check and login status)
        if (!ValidatePlayerState(pLeavingPlayer, pClientID)) {
            ExitResult result(ExitResult::Status::SerialMismatch, "Player state validation failed");
            LogExitAttempt(pClientID, result);
            return result;
        }

        // Store original party boss for comparison
        CPartyPlayer* pOriginalBoss = GetPartyBoss(pLeavingPlayer);
        CPartyPlayer* pNewBoss = pOriginalBoss;

        // Handle party notifications if player is in party mode
        std::vector<PartyMemberInfo> partyMembers;
        if (IsPartyMode(pLeavingPlayer)) {
            partyMembers = GetPartyMembers(pLeavingPlayer);
            
            // Notify party members about the leaving player
            if (!NotifyPartyMembers(partyMembers, pLeavingPlayer, true)) {
                ExitResult result(ExitResult::Status::NotificationFailed, "Failed to notify party members");
                LogExitAttempt(pClientID, result);
                return result;
            }
        }

        // Process the actual exit (equivalent to original CPartyPlayer::ExitWorld call)
        if (!ProcessPartyExit(pLeavingPlayer, &pNewBoss)) {
            ExitResult result(ExitResult::Status::PartyUpdateFailed, "Failed to process party exit");
            LogExitAttempt(pClientID, result);
            return result;
        }

        // Handle party boss change notifications
        if (pNewBoss && pNewBoss != pOriginalBoss) {
            // Get updated party members for the new boss
            std::vector<PartyMemberInfo> updatedMembers = GetPartyMembers(pNewBoss);
            
            // Notify party members about the new boss
            if (!NotifyNewBoss(updatedMembers, pNewBoss)) {
                ExitResult result(ExitResult::Status::NotificationFailed, "Failed to notify new party boss");
                LogExitAttempt(pClientID, result);
                return result;
            }
        }

        // Create successful result
        ExitResult result(ExitResult::Status::Success, "Player successfully exited world");
        result.pNewBoss = pNewBoss;
        result.affectedMembers = partyMembers;
        
        LogExitAttempt(pClientID, result);
        LogPartyUpdate(pLeavingPlayer, pOriginalBoss, pNewBoss);
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ExitWorld: " << e.what() << std::endl;
        ExitResult result(ExitResult::Status::SystemError, std::string("Exception: ") + e.what());
        LogExitAttempt(pClientID, result);
        return result;
    }
}

/**
 * Validation functions
 */
bool WorldAvatarExitManager::ValidateClientExit(const ClientID* pClientID) {
    return pClientID && 
           pClientID->IsValid() && 
           pClientID->wIndex < MAX_PLAYER_INDEX;
}

bool WorldAvatarExitManager::ValidatePlayerState(const CPartyPlayer* pPlayer, const ClientID* pClientID) {
    if (!pPlayer || !pClientID) {
        return false;
    }

    // Check serial number match and login status (equivalent to original validation)
    // This would need to be implemented based on actual CPartyPlayer structure
    // Original: pLeaver->m_id.dwSerial == v13->dwSerial && pLeaver->m_bLogin
    
    // For now, assume validation passes (this would be properly implemented)
    return IsPlayerLoggedIn(pPlayer);
}

/**
 * Party management functions
 */
std::vector<PartyMemberInfo> WorldAvatarExitManager::GetPartyMembers(CPartyPlayer* pPlayer) {
    std::vector<PartyMemberInfo> members;
    
    if (!pPlayer || !ExternalFunctions::CPartyPlayer_GetPtrPartyMember) {
        return members;
    }

    try {
        CPartyPlayer** pPartyMembers = ExternalFunctions::CPartyPlayer_GetPtrPartyMember(pPlayer);
        if (!pPartyMembers) {
            return members;
        }

        // Collect all valid party members (equivalent to original loop)
        for (int i = 0; i < MAX_PARTY_MEMBERS && pPartyMembers[i]; ++i) {
            CPartyPlayer* pMember = pPartyMembers[i];
            if (pMember && pMember != pPlayer && ValidatePartyMember(pMember)) {
                // Get zone index for the member (this would need proper implementation)
                uint16_t zoneIndex = 0; // pMember->m_wZoneIndex or similar
                members.emplace_back(pMember, zoneIndex);
            }
        }
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in GetPartyMembers: " << e.what() << std::endl;
    }

    return members;
}

bool WorldAvatarExitManager::NotifyPartyMembers(const std::vector<PartyMemberInfo>& members, 
                                                CPartyPlayer* pLeavingPlayer, 
                                                bool isLeaveNotification) {
    if (!pLeavingPlayer || !ExternalFunctions::CPlayer_SendMsg_PartyLeaveSelfResult) {
        return false;
    }

    try {
        for (const auto& memberInfo : members) {
            if (memberInfo.bIsValid && memberInfo.pMember) {
                CPlayer* pPlayer = GetPlayer(memberInfo.wZoneIndex);
                if (pPlayer) {
                    if (isLeaveNotification) {
                        ExternalFunctions::CPlayer_SendMsg_PartyLeaveSelfResult(pPlayer, pLeavingPlayer, 1);
                    }
                }
            }
        }
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyPartyMembers: " << e.what() << std::endl;
        return false;
    }
}

bool WorldAvatarExitManager::NotifyNewBoss(const std::vector<PartyMemberInfo>& members, 
                                           CPartyPlayer* pNewBoss) {
    if (!pNewBoss || !ExternalFunctions::CPlayer_SendMsg_PartySuccessResult) {
        return false;
    }

    try {
        for (const auto& memberInfo : members) {
            if (memberInfo.bIsValid && memberInfo.pMember) {
                CPlayer* pPlayer = GetPlayer(memberInfo.wZoneIndex);
                if (pPlayer) {
                    ExternalFunctions::CPlayer_SendMsg_PartySuccessResult(pPlayer, pNewBoss);
                }
            }
        }
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyNewBoss: " << e.what() << std::endl;
        return false;
    }
}

/**
 * Player management functions
 */
CPartyPlayer* WorldAvatarExitManager::GetPartyPlayer(uint16_t wIndex) {
    if (wIndex >= MAX_PLAYER_INDEX) {
        return nullptr;
    }

    // Equivalent to: (CPartyPlayer *)((char *)&g_PartyPlayer + 128 * (unsigned __int64)wIndex)
    return &g_PartyPlayer[wIndex];
}

CPlayer* WorldAvatarExitManager::GetPlayer(uint16_t wZoneIndex) {
    // Equivalent to: &g_Player + wZoneIndex
    // This would need proper bounds checking based on actual player array size
    return &g_Player[wZoneIndex];
}

bool WorldAvatarExitManager::IsPlayerLoggedIn(const CPartyPlayer* pPlayer) {
    if (!pPlayer) {
        return false;
    }

    // This would need to be implemented based on actual CPartyPlayer structure
    // Original check was: pLeaver->m_bLogin
    // For now, return true (this would be properly implemented)
    return true;
}

bool WorldAvatarExitManager::IsPartyMode(const CPartyPlayer* pPlayer) {
    if (!pPlayer || !ExternalFunctions::CPartyPlayer_IsPartyMode) {
        return false;
    }

    return ExternalFunctions::CPartyPlayer_IsPartyMode(pPlayer);
}

CPartyPlayer* WorldAvatarExitManager::GetPartyBoss(CPartyPlayer* pPlayer) {
    if (!pPlayer) {
        return nullptr;
    }

    // This would access pPlayer->m_pPartyBoss or similar
    // For now, return nullptr (this would be properly implemented)
    return nullptr;
}

bool WorldAvatarExitManager::ProcessPartyExit(CPartyPlayer* pPlayer, CPartyPlayer** ppNewBoss) {
    if (!pPlayer || !ppNewBoss || !ExternalFunctions::CPartyPlayer_ExitWorld) {
        return false;
    }

    return ExternalFunctions::CPartyPlayer_ExitWorld(pPlayer, ppNewBoss);
}

bool WorldAvatarExitManager::ValidatePartyMember(CPartyPlayer* pMember) {
    return pMember != nullptr;
    // Additional validation could be added here
}

/**
 * Logging functions
 */
void WorldAvatarExitManager::LogExitAttempt(const ClientID* pClientID, const ExitResult& result) {
    if (pClientID) {
        std::cout << "[WORLD_EXIT] Client: " << pClientID->wIndex << ":" << pClientID->dwSerial
                  << " Result: " << result.GetStatusString();
        if (!result.message.empty()) {
            std::cout << " (" << result.message << ")";
        }
        std::cout << std::endl;
    } else {
        std::cout << "[WORLD_EXIT] Invalid client ID, Result: " << result.GetStatusString() << std::endl;
    }
}

void WorldAvatarExitManager::LogPartyUpdate(CPartyPlayer* pLeavingPlayer, 
                                            CPartyPlayer* pOldBoss, 
                                            CPartyPlayer* pNewBoss) {
    std::cout << "[PARTY_UPDATE] Player exited";
    if (pOldBoss != pNewBoss) {
        std::cout << ", Boss changed";
    }
    std::cout << std::endl;
}

/**
 * Utility namespace implementation
 */
namespace WorldAvatarExitUtils {

    bool ValidateClientID(const ClientID* pClientID) {
        return pClientID && pClientID->IsValid();
    }

    std::string FormatClientInfo(const ClientID* pClientID) {
        if (!pClientID) {
            return "Invalid ClientID";
        }

        char buffer[64];
        sprintf_s(buffer, sizeof(buffer), "Client[%u:%u]", pClientID->wIndex, pClientID->dwSerial);
        return std::string(buffer);
    }

    bool IsValidPartyMember(CPartyPlayer* pMember) {
        return pMember != nullptr;
        // Additional validation could be added here based on actual CPartyPlayer structure
    }

    size_t CountActivePartyMembers(CPartyPlayer** pPartyMembers) {
        if (!pPartyMembers) {
            return 0;
        }

        size_t count = 0;
        for (int i = 0; i < WorldAvatarExitManager::MAX_PARTY_MEMBERS && pPartyMembers[i]; ++i) {
            if (IsValidPartyMember(pPartyMembers[i])) {
                ++count;
            }
        }
        return count;
    }

    CPartyPlayer* FindNextPartyBoss(CPartyPlayer** pPartyMembers, CPartyPlayer* pLeavingPlayer) {
        if (!pPartyMembers || !pLeavingPlayer) {
            return nullptr;
        }

        // Find the first valid party member that is not the leaving player
        for (int i = 0; i < WorldAvatarExitManager::MAX_PARTY_MEMBERS && pPartyMembers[i]; ++i) {
            CPartyPlayer* pMember = pPartyMembers[i];
            if (pMember && pMember != pLeavingPlayer && IsValidPartyMember(pMember)) {
                return pMember;
            }
        }
        return nullptr;
    }

    std::string FormatExitResult(const ExitResult& result) {
        std::string formatted = "ExitResult[";
        formatted += result.GetStatusString();
        if (!result.message.empty()) {
            formatted += ": " + result.message;
        }
        if (result.pNewBoss) {
            formatted += ", NewBoss: Yes";
        }
        formatted += ", AffectedMembers: " + std::to_string(result.affectedMembers.size());
        formatted += "]";
        return formatted;
    }

    std::string FormatPartyInfo(const std::vector<PartyMemberInfo>& members) {
        std::string info = "PartyMembers[" + std::to_string(members.size()) + "]: ";
        for (size_t i = 0; i < members.size(); ++i) {
            if (i > 0) info += ", ";
            info += "Zone:" + std::to_string(members[i].wZoneIndex);
            info += (members[i].bIsValid ? "(Valid)" : "(Invalid)");
        }
        return info;
    }

    ExitResult CreateErrorResult(ExitResult::Status status, const std::string& details) {
        return ExitResult(status, details);
    }

    void LogError(const std::string& function, const std::string& error) {
        std::cerr << "[ERROR] " << function << ": " << error << std::endl;
    }

    void LogInfo(const std::string& function, const std::string& info) {
        std::cout << "[INFO] " << function << ": " << info << std::endl;
    }
}

/**
 * Legacy C-style interface function
 */
extern "C" {
    void wa_ExitWorld(const ClientID* pidWorld) {
        ExitResult result = WorldAvatarExitManager::ExitWorld(pidWorld);
        // The original function was void, so we just call and ignore the result
        // In a real implementation, you might want to handle the result appropriately

        // Optional: Log the result for debugging
        if (!result.IsSuccess()) {
            WorldAvatarExitUtils::LogError("wa_ExitWorld",
                WorldAvatarExitUtils::FormatExitResult(result));
        }
    }
}

/**
 * Placeholder implementations for external function pointers
 * These would be properly initialized based on the actual game implementation
 */
namespace {
    // Initialize external function pointers with placeholder implementations
    void InitializeExternalFunctions() {
        // These would be set to actual function addresses in a real implementation
        ExternalFunctions::CPartyPlayer_ExitWorld = [](CPartyPlayer* player, CPartyPlayer** newBoss) -> bool {
            std::cout << "[DEBUG] CPartyPlayer::ExitWorld called" << std::endl;
            return true;
        };

        ExternalFunctions::CPartyPlayer_IsPartyMode = [](const CPartyPlayer* player) -> bool {
            std::cout << "[DEBUG] CPartyPlayer::IsPartyMode called" << std::endl;
            return false; // Default to not in party mode
        };

        ExternalFunctions::CPartyPlayer_GetPtrPartyMember = [](CPartyPlayer* player) -> CPartyPlayer** {
            std::cout << "[DEBUG] CPartyPlayer::GetPtrPartyMember called" << std::endl;
            return nullptr; // Default to no party members
        };

        ExternalFunctions::CPlayer_SendMsg_PartyLeaveSelfResult = [](CPlayer* player, CPartyPlayer* leaving, int result) -> void {
            std::cout << "[DEBUG] CPlayer::SendMsg_PartyLeaveSelfResult called" << std::endl;
        };

        ExternalFunctions::CPlayer_SendMsg_PartySuccessResult = [](CPlayer* player, CPartyPlayer* boss) -> void {
            std::cout << "[DEBUG] CPlayer::SendMsg_PartySuccessResult called" << std::endl;
        };
    }

    // Static initializer to set up function pointers
    struct ExternalFunctionInitializer {
        ExternalFunctionInitializer() {
            InitializeExternalFunctions();
        }
    };

    static ExternalFunctionInitializer g_initializer;
}
