/*
 * CMapDisplay.h - Map Display Management System
 * Refactored for Visual Studio 2022 compatibility
 * Original: 0CMapDisplayQEAAXZ_14019D560.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <vector>
#include <array>

// Windows API includes for display functionality
#ifdef _WIN32
#include <windows.h>
#include <wingdi.h>
#endif

// Forward declarations
class CDisplay;
class CCollLineDraw;
class CRect;
class CMyTimer;
class CFont;
class CMapExtend;
class CDummyDraw;
class CMapData;
class CGameObject;
class CSurface;

// Virtual function table structure
struct CDisplayVtbl {
    void* functions[32]; // Placeholder for virtual function pointers
};

/**
 * CDisplay base class for display functionality
 */
class CDisplay {
public:
    CDisplay();
    virtual ~CDisplay();
    
    virtual void Initialize();
    virtual void Reset();
    virtual void Update();
    virtual void Render();
    
protected:
    CDisplayVtbl* vfptr;
    bool m_bInitialized;
};

/**
 * CCollLineDraw class for collision line drawing
 */
class CCollLineDraw {
public:
    CCollLineDraw();
    ~CCollLineDraw();

    void Initialize();
    void Reset();
    void DrawLine(float x1, float y1, float x2, float y2);
    void SetColor(uint32_t color);
    static void InitPen();
    static void DeletePen();
    
private:
    bool m_bInitialized;
    uint32_t m_color;
    void* m_pPen;
};

/**
 * CRect class for rectangle management
 */
class CRect {
public:
    CRect();
    CRect(int left, int top, int right, int bottom);
    ~CRect();

    void Initialize();
    void SetRect(int left, int top, int right, int bottom);
    void GetRect(int& left, int& top, int& right, int& bottom) const;
    bool IsEmpty() const;
    void Clear();
    
private:
    int m_left, m_top, m_right, m_bottom;
};

/**
 * CMyTimer class for timer management (if not already defined)
 */
#ifndef CMYTIMER_MAPDISPLAY_DEFINED
#define CMYTIMER_MAPDISPLAY_DEFINED
class CMyTimer {
public:
    CMyTimer();
    ~CMyTimer();
    
    void Initialize();
    void Reset();
    void BeginTimer(uint32_t duration);
    void Start();
    void Stop();
    void Update();
    bool IsActive() const { return m_bActive; }
    bool IsExpired() const;
    uint32_t GetElapsedTime() const { return m_dwElapsedTime; }
    uint32_t GetRemainingTime() const;
    
private:
    bool m_bActive;
    uint32_t m_dwStartTime;
    uint32_t m_dwElapsedTime;
    uint32_t m_dwDuration;
    bool m_bExpired;
};
#endif

/**
 * CFont class for font management
 */
class CFont {
public:
    CFont();
    ~CFont();
    
    void Initialize();
    void Reset();
    void CreateFontA(int height, int width, int escapement, int orientation, 
                     int weight, bool italic, bool underline, bool strikeout,
                     uint8_t charset, uint8_t outPrecision, uint8_t clipPrecision,
                     uint8_t quality, uint8_t pitchAndFamily, const char* faceName);
    void SetFont(const char* fontName, int size);
    bool IsValid() const { return m_bValid; }
    
private:
    void* m_hFont;
    bool m_bValid;
    std::string m_fontName;
    int m_fontSize;
};

/**
 * CMapExtend class for map extension functionality
 */
class CMapExtend {
public:
    CMapExtend();
    ~CMapExtend();
    
    void Initialize();
    void Reset();
    void Init(CSurface** ppSFMap);
    void Update();
    void Render();
    
private:
    bool m_bInitialized;
    CSurface** m_ppSFMap;
};

/**
 * CDummyDraw class for dummy drawing functionality
 */
class CDummyDraw {
public:
    CDummyDraw();
    ~CDummyDraw();

    void Initialize();
    void Reset();
    void Draw();
    static void InitPen();
    static void DeletePen();
    
private:
    bool m_bInitialized;
};

/**
 * CSurface class for surface management
 */
class CSurface {
public:
    CSurface();
    ~CSurface();
    
    void Initialize();
    void Reset();
    void CreateSurface(int width, int height);
    void ReleaseSurface();
    bool IsValid() const { return m_bValid; }
    
private:
    void* m_pSurface;
    bool m_bValid;
    int m_width, m_height;
};

/**
 * Map Display Management System
 * Handles comprehensive map display with collision lines, timers, fonts, and surface objects
 */
class CMapDisplay : public CDisplay {
public:
    // Constructor/Destructor
    CMapDisplay();
    virtual ~CMapDisplay();

    // Core map display functionality
    void InitializeMapDisplay();
    void InitializeMapDisplayExact();  // Follows exact original constructor order
    void InitializeCollisionLines();
    void InitializeTimerSystems();
    void InitializeFontSystems();
    void InitializeMapExtension();
    void InitializeDummyDrawing();
    void InitializeSurfaceObjects();
    void InitializePenSystems();
    
    // Collision line management
    void SetupCollisionLines();
    void DrawCollisionLines();
    void UpdateCollisionLines();
    void ClearCollisionLines();
    
    // Display mode management
    void SetDisplayMode(bool mode) { m_bDisplayMode = mode; }
    bool GetDisplayMode() const { return m_bDisplayMode; }
    void ToggleDisplayMode() { m_bDisplayMode = !m_bDisplayMode; }
    
    // Map management
    void SetActiveMap(CMapData* pMap) { m_pActMap = pMap; }
    CMapData* GetActiveMap() const { return m_pActMap; }
    void SetOldActiveMap(CMapData* pMap) { m_pOldActMap = pMap; }
    CMapData* GetOldActiveMap() const { return m_pOldActMap; }
    void ChangeMap(CMapData* pNewMap);
    
    // Layer management
    void SetLayerIndex(uint16_t layerIndex) { m_wLayerIndex = layerIndex; }
    uint16_t GetLayerIndex() const { return m_wLayerIndex; }
    void ChangeLayer(uint16_t newLayer);
    
    // Window rectangle management
    void SetWindowRect(const CRect& rect) { m_rcWnd = rect; }
    const CRect& GetWindowRect() const { return m_rcWnd; }
    void UpdateWindowRect();
    
    // Timer management
    void StartDrawTimer();
    void StopDrawTimer();
    void UpdateDrawTimer();
    bool IsDrawTimerActive() const;
    
    // Font management
    void SetupFont();
    void UpdateFont();
    void RenderText(const char* text, int x, int y);
    
    // Map extension management
    void SetupMapExtension();
    void UpdateMapExtension();
    void RenderMapExtension();
    
    // Dummy drawing management
    void SetupDummyDrawing();
    void UpdateDummyDrawing();
    void RenderDummyDrawing();
    void SetDummyDraw(int index, CDummyDraw* pDummy);
    CDummyDraw* GetDummyDraw(int index) const;
    void SetDummyDrawNum(int index, int num);
    int GetDummyDrawNum(int index) const;
    
    // Surface object management
    void SetupSurfaceObjects();
    void UpdateSurfaceObjects();
    void RenderSurfaceObjects();
    void SetSurfaceObject(int type, int index, CSurface* pSurface);
    CSurface* GetSurfaceObject(int type, int index) const;
    void ClearSurfaceObjects();
    
    // Pen management
    void SetupPenSystems();
    void CreateBorderPen();
    void ReleasePenSystems();
    
    // Rendering
    void DrawDisplay();
    void DrawMap();
    void DrawObjects();
    void DrawDummies();
    void DrawCollisions();
    void DrawText();
    void SelectObject(CGameObject* pObject, CSurface* pSurface);
    void ReleaseDisplay();
    
    // Validation and error handling
    bool ValidateMapDisplay() const;
    bool ValidateCollisionLines() const;
    bool ValidateSurfaceObjects() const;
    bool ValidateDummyDrawing() const;
    
    // Logging and debugging
    void LogMapDisplayInitialization() const;
    void LogCollisionLineSetup() const;
    void LogSurfaceObjectSetup() const;
    void LogDummyDrawSetup() const;

private:
    // Internal data members (equivalent to original structure)
    
    // Collision line drawing array (60 elements)
    std::array<CCollLineDraw, 60> m_CollLineDraw;
    
    // Window rectangle
    CRect m_rcWnd;
    
    // Timer system
    CMyTimer m_tmrDraw;
    
    // Font system
    CFont m_Font;
    
    // Map extension
    CMapExtend m_MapExtend;
    
    // Display state
    bool m_bDisplayMode;                               // Display mode flag
    CMapData* m_pActMap;                              // Active map pointer
    uint16_t m_wLayerIndex;                           // Layer index
    CMapData* m_pOldActMap;                           // Old active map pointer
    
    // Dummy drawing arrays (60 elements each)
    std::array<CDummyDraw*, 60> m_DummyDraw;          // Dummy draw objects
    std::array<int, 60> m_nDummyDrawNum;              // Dummy draw numbers
    
    // Pen system
    void* m_hPenBorder;                               // Border pen handle
    
    // Surface objects (2x13 array)
    std::array<std::array<CSurface*, 13>, 2> m_pSFObj; // Surface object array
    
    // Special surface objects
    CSurface* m_pSFMap;                               // Map surface
    CSurface* m_pSFSelect;                            // Selection surface
    CSurface* m_pSFCircle;                            // Circle surface
    CSurface* m_pSFBuf;                               // Buffer surface
    CSurface* m_pSFCorpse;                            // Corpse surface
    
    // Internal processing helpers
    void InitializeProcessingContext();
    void CleanupProcessingContext();
    void DestructorCleanupExact();  // Follows exact original destructor order
    void SetupInternalStructures();
    void ConfigureDefaultParameters();
    void AllocateMemoryForSystems();
    void DeallocateMemoryForSystems();
    
    // Constants
    static constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    static constexpr size_t STACK_INIT_SIZE = 36 * sizeof(uint32_t);
    static constexpr int64_t INIT_MARKER = -2;
    static constexpr uint32_t DRAW_TIMER_DURATION = 0x64;       // 100 milliseconds
    static constexpr int COLLISION_LINE_COUNT = 60;
    static constexpr int DUMMY_DRAW_COUNT = 60;
    static constexpr int SURFACE_OBJ_TYPES = 2;
    static constexpr int SURFACE_OBJ_COUNT = 13;
    static constexpr bool DEFAULT_DISPLAY_MODE = false;
    static constexpr uint16_t DEFAULT_LAYER_INDEX = 0;
    static constexpr int DEFAULT_FONT_SIZE = 15;
    static constexpr int DEFAULT_FONT_WEIGHT = 400;
    static constexpr uint32_t DEFAULT_FONT_CHARSET = 0x20;
    static constexpr int DEFAULT_PEN_WIDTH = 2;
    static constexpr int DEFAULT_PEN_STYLE = 0;
    static constexpr uint32_t DEFAULT_PEN_COLOR = 0;
    
    // Disable copy constructor and assignment operator
    CMapDisplay(const CMapDisplay&) = delete;
    CMapDisplay& operator=(const CMapDisplay&) = delete;
};

/**
 * Legacy C-style interface for compatibility
 * These functions maintain the original calling convention for existing code
 */
namespace CMapDisplayLegacy {
    // Original constructor signature for compatibility
    void CMapDisplay_Constructor(CMapDisplay* pThis);
    
    // Helper functions for legacy integration
    void InitializeLegacySupport();
    void CleanupLegacySupport();
}

/**
 * Utility functions for map display management
 */
namespace CMapDisplayUtils {
    // Validation utilities
    bool IsValidMapDisplay(const CMapDisplay* pMapDisplay);
    bool IsValidCollLineDraw(const CCollLineDraw* pCollLine);
    bool IsValidSurface(const CSurface* pSurface);
    bool IsValidFont(const CFont* pFont);
    
    // String utilities
    std::string SafeStringCopy(const char* source, size_t maxLength = 256);
    std::string FormatMapDisplayInfo(const CMapDisplay* pMapDisplay);
    std::string FormatCollLineInfo(const CCollLineDraw* pCollLine);
    std::string FormatSurfaceInfo(const CSurface* pSurface);
    
    // Pen utilities
    void* CreatePen(int style, int width, uint32_t color);
    void DeletePen(void* hPen);
    bool IsValidPen(void* hPen);
    
    // Logging utilities
    void LogMapDisplayCall(const char* functionName, const CMapDisplay* pMapDisplay, const char* details = nullptr);
    void LogError(const char* errorMessage, const char* context = nullptr);
    void LogMapDisplayOperation(const char* operation, const CMapDisplay* pMapDisplay, bool success);
    void LogCollLineOperation(const char* operation, const CCollLineDraw* pCollLine, bool success);
    void LogSurfaceOperation(const char* operation, const CSurface* pSurface, bool success);
    void LogFontOperation(const char* operation, const CFont* pFont, bool success);
}

// Constants for map display processing
namespace CMapDisplayConstants {
    constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    constexpr size_t STACK_INIT_SIZE = 36 * sizeof(uint32_t);
    constexpr int64_t INIT_MARKER = -2;
    constexpr uint32_t DRAW_TIMER_DURATION = 0x64;       // 100 milliseconds
    constexpr int COLLISION_LINE_COUNT = 60;
    constexpr int DUMMY_DRAW_COUNT = 60;
    constexpr int SURFACE_OBJ_TYPES = 2;
    constexpr int SURFACE_OBJ_COUNT = 13;
    constexpr bool DEFAULT_DISPLAY_MODE = false;
    constexpr uint16_t DEFAULT_LAYER_INDEX = 0;
    constexpr int DEFAULT_FONT_SIZE = 15;
    constexpr int DEFAULT_FONT_WEIGHT = 400;
    constexpr uint32_t DEFAULT_FONT_CHARSET = 0x20;
    constexpr int DEFAULT_PEN_WIDTH = 2;
    constexpr int DEFAULT_PEN_STYLE = 0;
    constexpr uint32_t DEFAULT_PEN_COLOR = 0;
    
    // Font constants
    constexpr const char* DEFAULT_FONT_NAME = "Arial";
    
    // Display operation types
    constexpr const char* OPERATION_TYPE_INITIALIZATION = "Initialization";
    constexpr const char* OPERATION_TYPE_COLLISION_SETUP = "CollisionSetup";
    constexpr const char* OPERATION_TYPE_SURFACE_SETUP = "SurfaceSetup";
    constexpr const char* OPERATION_TYPE_DUMMY_SETUP = "DummySetup";
    constexpr const char* OPERATION_TYPE_FONT_SETUP = "FontSetup";
    constexpr const char* OPERATION_TYPE_PEN_SETUP = "PenSetup";
}
