/*
 * CMapExtend.h - Map Extension Management System
 * Refactored for Visual Studio 2022 compatibility
 * Original: 0CMapExtendQEAAXZ_1401A1410.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>

// Windows API includes for drawing functionality
#ifdef _WIN32
#include <windows.h>
#include <wingdi.h>
#endif

// Forward declarations
class CSurface;

/**
 * CPoint class for point management
 */
class CPoint {
public:
    CPoint();
    CPoint(int x, int y);
    ~CPoint();
    
    void SetPoint(int x, int y);
    void GetPoint(int& x, int& y) const;
    void Offset(int dx, int dy);
    void Clear();
    
    // Operators
    CPoint operator+(const CPoint& other) const;
    CPoint operator-(const CPoint& other) const;
    CPoint& operator+=(const CPoint& other);
    CPoint& operator-=(const CPoint& other);
    bool operator==(const CPoint& other) const;
    bool operator!=(const CPoint& other) const;
    
    // Accessors
    int GetX() const { return m_x; }
    int GetY() const { return m_y; }
    void SetX(int x) { m_x = x; }
    void SetY(int y) { m_y = y; }
    
private:
    int m_x, m_y;
};

/**
 * CRect class for rectangle management
 */
class CRect {
public:
    CRect();
    CRect(int left, int top, int right, int bottom);
    CRect(const CPoint& topLeft, const CPoint& bottomRight);
    ~CRect();
    
    void SetRect(int left, int top, int right, int bottom);
    void GetRect(int& left, int& top, int& right, int& bottom) const;
    void SetRectEmpty();
    bool IsRectEmpty() const;
    void InflateRect(int dx, int dy);
    void DeflateRect(int dx, int dy);
    void OffsetRect(int dx, int dy);
    bool PtInRect(const CPoint& point) const;
    
    // Accessors
    int GetLeft() const { return m_left; }
    int GetTop() const { return m_top; }
    int GetRight() const { return m_right; }
    int GetBottom() const { return m_bottom; }
    int GetWidth() const { return m_right - m_left; }
    int GetHeight() const { return m_bottom - m_top; }
    
    void SetLeft(int left) { m_left = left; }
    void SetTop(int top) { m_top = top; }
    void SetRight(int right) { m_right = right; }
    void SetBottom(int bottom) { m_bottom = bottom; }
    
private:
    int m_left, m_top, m_right, m_bottom;
};

/**
 * CSize class for size management
 */
class CSize {
public:
    CSize();
    CSize(int width, int height);
    ~CSize();
    
    void SetSize(int width, int height);
    void GetSize(int& width, int& height) const;
    void Clear();
    
    // Operators
    CSize operator+(const CSize& other) const;
    CSize operator-(const CSize& other) const;
    CSize& operator+=(const CSize& other);
    CSize& operator-=(const CSize& other);
    bool operator==(const CSize& other) const;
    bool operator!=(const CSize& other) const;
    
    // Accessors
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    void SetWidth(int width) { m_width = width; }
    void SetHeight(int height) { m_height = height; }
    
private:
    int m_width, m_height;
};

/**
 * Map Extension Management System
 * Handles comprehensive map extension with point systems, rectangles, and drawing operations
 */
class CMapExtend {
public:
    // Constructor/Destructor
    CMapExtend();
    virtual ~CMapExtend();

    // Core map extension functionality
    void InitializeMapExtend();
    void InitializePointSystems();
    void InitializeRectangleSystems();
    void InitializeSizeSystems();
    void InitializePenSystems();
    void InitializeAreaSettings();
    void InitializeExtendMode();
    
    // Point management
    void SetStartMapPoint(const CPoint& point) { m_ptStartMap = point; }
    const CPoint& GetStartMapPoint() const { return m_ptStartMap; }
    void SetEndMapPoint(const CPoint& point) { m_ptEndMap = point; }
    const CPoint& GetEndMapPoint() const { return m_ptEndMap; }
    void SetCenterPoint(const CPoint& point) { m_ptCenter = point; }
    const CPoint& GetCenterPoint() const { return m_ptCenter; }
    
    // Screen coordinate management
    void SetStartScreenPoint(const CPoint& point) { m_ptStartScreen = point; }
    const CPoint& GetStartScreenPoint() const { return m_ptStartScreen; }
    void SetEndScreenPoint(const CPoint& point) { m_ptEndScreen = point; }
    const CPoint& GetEndScreenPoint() const { return m_ptEndScreen; }
    void SetMoveScreenPoint(const CPoint& point) { m_ptMoveScreen = point; }
    const CPoint& GetMoveScreenPoint() const { return m_ptMoveScreen; }
    
    // Rectangle management
    void SetExtendRect(const CRect& rect) { m_rcExtend = rect; }
    const CRect& GetExtendRect() const { return m_rcExtend; }
    void UpdateExtendRect();
    void CalculateExtendRect();
    
    // Size management
    void SetExtendSize(const CSize& size) { m_sizeExtend = size; }
    const CSize& GetExtendSize() const { return m_sizeExtend; }
    void UpdateExtendSize();
    void CalculateExtendSize();
    
    // Area settings
    void SetAreaFlag(bool bSetArea) { m_bSetArea = bSetArea; }
    bool GetAreaFlag() const { return m_bSetArea; }
    void ToggleAreaFlag() { m_bSetArea = !m_bSetArea; }
    
    // Extend mode management
    void SetExtendMode(bool bExtendMode) { m_bExtendMode = bExtendMode; }
    bool GetExtendMode() const { return m_bExtendMode; }
    void ToggleExtendMode() { m_bExtendMode = !m_bExtendMode; }
    
    // Pen management
    void CreateExtendPen();
    void ReleaseExtendPen();
    void* GetExtendPen() const { return m_hPen; }
    bool IsExtendPenValid() const { return m_hPen != nullptr; }
    
    // Map coordinate conversion
    CPoint MapToScreen(const CPoint& mapPoint) const;
    CPoint ScreenToMap(const CPoint& screenPoint) const;
    void UpdateCoordinateMapping();
    
    // Extension calculations
    void CalculateMapBounds();
    void CalculateScreenBounds();
    void CalculateExtensionArea();
    bool IsPointInExtension(const CPoint& point) const;
    
    // Drawing operations
    void DrawExtensionBorder();
    void DrawExtensionArea();
    void DrawCoordinateGrid();
    void DrawMapBounds();
    
    // Surface integration
    void Init(CSurface** ppSFMap);
    void SetMapSurface(CSurface** ppSFMap) { m_ppSFMap = ppSFMap; }
    CSurface** GetMapSurface() const { return m_ppSFMap; }
    
    // Update and rendering
    void Update();
    void Render();
    void FrameMove();
    
    // Scrolling operations
    void ScrollMapUp(int distance);
    void ScrollMapDown(int distance);
    void ScrollMapLeft(int distance);
    void ScrollMapRight(int distance);
    void ScrollMapTo(const CPoint& targetPoint);
    
    // Validation and error handling
    bool ValidateMapExtend() const;
    bool ValidatePointSystems() const;
    bool ValidateRectangleSystems() const;
    bool ValidatePenSystems() const;
    
    // Logging and debugging
    void LogMapExtendInitialization() const;
    void LogPointSystemSetup() const;
    void LogRectangleSystemSetup() const;
    void LogPenSystemSetup() const;

private:
    // Internal data members (equivalent to original structure)
    
    // Point systems
    CPoint m_ptStartMap;                              // Start map point
    CPoint m_ptEndMap;                                // End map point
    CPoint m_ptCenter;                                // Center point
    CPoint m_ptStartScreen;                           // Start screen point
    CPoint m_ptEndScreen;                             // End screen point
    CPoint m_ptMoveScreen;                            // Move screen point
    
    // Rectangle system
    CRect m_rcExtend;                                 // Extension rectangle
    
    // Size system
    CSize m_sizeExtend;                               // Extension size
    
    // State flags
    bool m_bSetArea;                                  // Area set flag
    bool m_bExtendMode;                               // Extend mode flag
    
    // Drawing system
    void* m_hPen;                                     // Pen handle for drawing
    
    // Surface integration
    CSurface** m_ppSFMap;                             // Map surface pointer
    
    // Internal processing helpers
    void InitializeProcessingContext();
    void CleanupProcessingContext();
    void SetupInternalStructures();
    void ConfigureDefaultParameters();
    void AllocateMemoryForSystems();
    void DeallocateMemoryForSystems();
    
    // Constants
    static constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    static constexpr size_t STACK_INIT_SIZE = 8 * sizeof(uint32_t);
    static constexpr int DEFAULT_PEN_STYLE = 0;                 // PS_SOLID
    static constexpr int DEFAULT_PEN_WIDTH = 1;
    static constexpr uint32_t DEFAULT_PEN_COLOR = 0x646464;     // Gray color
    static constexpr bool DEFAULT_AREA_FLAG = false;
    static constexpr bool DEFAULT_EXTEND_MODE = false;
    
    // Disable copy constructor and assignment operator
    CMapExtend(const CMapExtend&) = delete;
    CMapExtend& operator=(const CMapExtend&) = delete;
};

/**
 * Legacy C-style interface for compatibility
 * These functions maintain the original calling convention for existing code
 */
namespace CMapExtendLegacy {
    // Original constructor signature for compatibility
    void CMapExtend_Constructor(CMapExtend* pThis);
    
    // Helper functions for legacy integration
    void InitializeLegacySupport();
    void CleanupLegacySupport();
}

/**
 * Utility functions for map extension management
 */
namespace CMapExtendUtils {
    // Validation utilities
    bool IsValidMapExtend(const CMapExtend* pMapExtend);
    bool IsValidPoint(const CPoint* pPoint);
    bool IsValidRect(const CRect* pRect);
    bool IsValidSize(const CSize* pSize);
    
    // String utilities
    std::string SafeStringCopy(const char* source, size_t maxLength = 256);
    std::string FormatMapExtendInfo(const CMapExtend* pMapExtend);
    std::string FormatPointInfo(const CPoint* pPoint);
    std::string FormatRectInfo(const CRect* pRect);
    std::string FormatSizeInfo(const CSize* pSize);
    
    // Pen utilities
    void* CreatePen(int style, int width, uint32_t color);
    void DeletePen(void* hPen);
    bool IsValidPen(void* hPen);
    
    // Coordinate utilities
    double CalculateDistance(const CPoint& p1, const CPoint& p2);
    CPoint CalculateMidpoint(const CPoint& p1, const CPoint& p2);
    bool IsPointInRect(const CPoint& point, const CRect& rect);
    
    // Logging utilities
    void LogMapExtendCall(const char* functionName, const CMapExtend* pMapExtend, const char* details = nullptr);
    void LogError(const char* errorMessage, const char* context = nullptr);
    void LogMapExtendOperation(const char* operation, const CMapExtend* pMapExtend, bool success);
    void LogPointOperation(const char* operation, const CPoint* pPoint, bool success);
    void LogRectOperation(const char* operation, const CRect* pRect, bool success);
    void LogSizeOperation(const char* operation, const CSize* pSize, bool success);
    void LogPenOperation(const char* operation, void* hPen, bool success);
}

// Constants for map extension processing
namespace CMapExtendConstants {
    constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    constexpr size_t STACK_INIT_SIZE = 8 * sizeof(uint32_t);
    constexpr int DEFAULT_PEN_STYLE = 0;                 // PS_SOLID
    constexpr int DEFAULT_PEN_WIDTH = 1;
    constexpr uint32_t DEFAULT_PEN_COLOR = 0x646464;     // Gray color
    constexpr bool DEFAULT_AREA_FLAG = false;
    constexpr bool DEFAULT_EXTEND_MODE = false;
    
    // Point operation types
    constexpr const char* OPERATION_TYPE_INITIALIZATION = "Initialization";
    constexpr const char* OPERATION_TYPE_POINT_SETUP = "PointSetup";
    constexpr const char* OPERATION_TYPE_RECT_SETUP = "RectSetup";
    constexpr const char* OPERATION_TYPE_SIZE_SETUP = "SizeSetup";
    constexpr const char* OPERATION_TYPE_PEN_SETUP = "PenSetup";
    constexpr const char* OPERATION_TYPE_COORDINATE_CONVERSION = "CoordinateConversion";
    constexpr const char* OPERATION_TYPE_SCROLLING = "Scrolling";
}
