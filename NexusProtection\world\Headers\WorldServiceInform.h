/*
 * WorldServiceInform.h - World Service Information Handler
 * Refactored for Visual Studio 2022 compatibility
 * Original: WorldServiceInformCNetworkEXAEAA_NKPEADZ_1401C0940.c
 */

#pragma once

#include <cstdint>
#include <memory>

// Forward declarations
class CNetworkEX;
class CMainThread;

// External dependencies (these would need to be properly defined)
extern CMainThread g_Main;

/**
 * World Service Information Handler
 * Handles world service information messages from the network layer
 */
class WorldServiceInformHandler {
public:
    // Constructor/Destructor
    WorldServiceInformHandler();
    virtual ~WorldServiceInformHandler();

    // Main service information processing
    static bool ProcessWorldServiceInform(CNetworkEX* pNetwork, uint32_t messageSize, char* pMessage);
    
    // Validation functions
    static bool ValidateServiceMessage(uint32_t messageSize, const char* pMessage);
    static bool ValidateNetworkInstance(const CNetworkEX* pNetwork);
    
    // Utility functions
    static bool ForwardToMainThread(char serviceCode);
    
    // Error handling
    static void HandleServiceError(const char* errorMessage);
    static void LogServiceActivity(uint32_t messageSize, char serviceCode);

private:
    // Internal processing helpers
    static bool ProcessServiceCode(char serviceCode);
    static void InitializeProcessingContext();
    static void CleanupProcessingContext();
    
    // Constants
    static constexpr uint32_t MAX_MESSAGE_SIZE = 4096;
    static constexpr uint32_t MIN_MESSAGE_SIZE = 1;
    
    // Disable copy constructor and assignment operator
    WorldServiceInformHandler(const WorldServiceInformHandler&) = delete;
    WorldServiceInformHandler& operator=(const WorldServiceInformHandler&) = delete;
};

/**
 * Legacy C-style interface for compatibility
 * These functions maintain the original calling convention for existing code
 */
namespace WorldServiceInformLegacy {
    // Original function signature for compatibility
    bool WorldServiceInform(CNetworkEX* pThis, uint32_t messageSize, char* pMessage);
    
    // Helper functions for legacy integration
    void InitializeLegacySupport();
    void CleanupLegacySupport();
}

/**
 * Utility functions for world service management
 */
namespace WorldServiceUtils {
    // Service code validation
    bool IsValidServiceCode(char serviceCode);
    
    // Message processing utilities
    std::unique_ptr<char[]> CreateSafeMessageCopy(const char* pMessage, uint32_t size);
    bool VerifyMessageIntegrity(const char* pMessage, uint32_t size);
    
    // Logging and debugging
    void LogServiceCall(const char* functionName, uint32_t messageSize, char serviceCode);
    void LogError(const char* errorMessage, const char* context = nullptr);
}

// Type definitions for better code clarity
using ServiceMessageHandler = bool(*)(uint32_t, char*);
using ServiceErrorCallback = void(*)(const char*);

// Constants for service processing
namespace WorldServiceConstants {
    constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    constexpr size_t STACK_INIT_SIZE = 12 * sizeof(uint64_t);
    constexpr char DEFAULT_SERVICE_CODE = 0;
}
