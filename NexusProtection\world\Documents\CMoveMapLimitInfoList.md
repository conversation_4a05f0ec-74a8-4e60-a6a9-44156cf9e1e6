# CMoveMapLimitInfoList

## Overview

The `CMoveMapLimitInfoList` class is a container that manages a collection of `CMoveMapLimitInfo` objects. It provides centralized management of map movement limitation information within the NexusProtection game system.

## Purpose

This class serves as a container and manager for:
- **Movement Limitation Objects**: Storing and organizing CMoveMapLimitInfo instances
- **Player-Specific Loading**: Loading limitation data based on player information and rights
- **Request Processing**: Handling movement limitation requests and delegating to appropriate objects
- **Information Retrieval**: Finding specific limitation information by various criteria

## Architecture

### Container Design
The class uses a `std::vector<CMoveMapLimitInfo*>` to store pointers to limitation information objects, providing efficient access and iteration.

### Key Components
- **CMoveMapLimitInfo**: Individual limitation information objects
- **CMoveMapLimitRightInfo**: User rights information for access control
- **CPlayer**: Player object containing user-specific data

## Class Interface

### Core Methods
- `Init(vecRightTypeList)`: Initializes the list with right type information
- `Load(pkPlayer, pkRight)`: Loads player-specific limitation data
- `Request(...)`: Processes movement limitation requests
- `Get(...)`: Retrieves specific limitation information by criteria

### Utility Methods
- `CleanUp()`: Safely removes all limitation objects
- `Size()`: Returns the number of limitation objects
- `Empty()`: Checks if the list is empty

## Usage Example

```cpp
// Create and initialize the limitation info list
CMoveMapLimitInfoList limitInfoList;
std::vector<int> rightTypes = {1, 2, 3, 4}; // Example right types

if (limitInfoList.Init(rightTypes))
{
    // Load player-specific data
    limitInfoList.Load(pPlayer, pPlayerRights);
    
    // Process a movement request
    char result = limitInfoList.Request(limitType, requestType, mapIndex,
                                       storeRecordIndex, userIndex, 
                                       pRequestData, pPlayerRights);
    
    // Find specific limitation info
    CMoveMapLimitInfo* pInfo = limitInfoList.Get(limitType, mapIndex, storeRecordIndex);
    
    if (pInfo)
    {
        // Use the limitation information
    }
}

// Cleanup is automatic in destructor
```

## Dependencies

### Headers Required
- `CMoveMapLimitInfo.h`
- `CMoveMapLimitRightInfo.h`
- `CPlayer.h`
- Standard C++ containers (`<vector>`, `<memory>`)

### Related Classes
- **CMoveMapLimitInfo**: Individual limitation information objects
- **CMoveMapLimitRightInfo**: User rights and permissions
- **CPlayer**: Player object with user data
- **CMoveMapLimitManager**: Parent manager class

## Implementation Notes

### Memory Management
- Uses RAII principles for automatic cleanup
- Properly manages dynamically allocated CMoveMapLimitInfo objects
- Exception-safe operations with proper cleanup in destructors

### Performance Considerations
- Vector container provides efficient iteration and access
- Reserve space during initialization for better performance
- Lazy creation of limitation objects when needed

### Error Handling
- Comprehensive exception handling in all public methods
- Graceful fallbacks for error conditions
- Null pointer checks for all input parameters

## Refactoring Notes

This class was refactored from decompiled C source files to modern C++17/C++20 standards:

### Original Files Consolidated
- `0CMoveMapLimitInfoListQEAAXZ_1403A1DC0.c` (Constructor)
- `1CMoveMapLimitInfoListQEAAXZ_1403A1FA0.c` (Destructor)
- `LoadCMoveMapLimitInfoListQEAAXPEAVCPlayerPEAVCMove_1403A58F0.c` (Load method)
- `RequestCMoveMapLimitInfoListQEAAEHHHKHPEADPEAVCMov_1403A5F80.c` (Request method)
- `GetCMoveMapLimitInfoListAEAAPEAVCMoveMapLimitInfoH_1403A6020.c` (Get method)
- `CleanUpCMoveMapLimitInfoListAEAAXXZ_1403A6290.c` (CleanUp method)

### Modernization Changes
- Converted from C-style to modern C++ class design
- Added STL container usage (`std::vector`)
- Implemented exception safety and RAII principles
- Added comprehensive error handling
- Improved const-correctness and method signatures
- Added utility methods for better usability

## Testing Recommendations

1. **Unit Tests**: Test initialization, loading, and request processing
2. **Container Tests**: Verify proper object management and cleanup
3. **Integration Tests**: Test interaction with CMoveMapLimitInfo objects
4. **Memory Tests**: Validate proper memory management and no leaks
5. **Error Tests**: Verify graceful handling of error conditions

## Future Enhancements

- Consider adding iterator support for STL compatibility
- Implement search optimization for large datasets
- Add configuration-based initialization
- Consider thread safety if multi-threading is required
- Add performance monitoring and metrics

## Related Documentation

- [CMoveMapLimitManager.md](CMoveMapLimitManager.md) - Parent manager class
- [CMoveMapLimitInfo.md](CMoveMapLimitInfo.md) - Individual limitation objects
- [CMoveMapLimitRightInfo.md](CMoveMapLimitRightInfo.md) - Rights management
