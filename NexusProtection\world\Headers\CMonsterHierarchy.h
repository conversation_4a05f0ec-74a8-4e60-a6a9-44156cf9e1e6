#pragma once

#include <cstdint>
#include <memory>
#include <array>
#include <vector>

// Forward declarations
class CMonster;
class CMapData;
struct _monster_fld;

// Virtual function table structure
struct CMonsterHierarchyVtbl {
    void* functions[16]; // Placeholder for virtual function pointers
};

/**
 * @class CMonsterHierarchy
 * @brief Manages parent-child relationships between monsters
 * 
 * This class handles hierarchical relationships between monsters, allowing
 * parent monsters to spawn and manage child monsters. It supports multiple
 * types of child monsters and automatic regeneration based on timing.
 * 
 * Key Features:
 * - Parent-child monster relationships
 * - Multiple child monster types (up to 3 kinds)
 * - Automatic child monster regeneration
 * - Child monster recall and management
 * - Hierarchical monster spawning
 * 
 * @note Refactored from decompiled CMonsterHierarchy structure
 */
class CMonsterHierarchy {
public:
    // Constants
    static constexpr int MAX_CHILD_KINDS = 3;
    static constexpr int MAX_CHILDREN_PER_KIND = 10;
    static constexpr uint32_t INVALID_SERIAL = 0xFFFFFFFF;
    static constexpr uint32_t DEFAULT_RECALL_TIME = 30000; // 30 seconds

    /**
     * @brief Default constructor
     * Initializes the hierarchy with default values
     */
    CMonsterHierarchy();

    /**
     * @brief Virtual destructor
     * Cleans up resources and maintains virtual function table
     */
    virtual ~CMonsterHierarchy();

    /**
     * @brief Initializes the hierarchy system
     * Resets all child references and counters
     */
    void Init();

    /**
     * @brief One-time initialization with monster reference
     * @param pMonster Pointer to the monster this hierarchy belongs to
     */
    void OnlyOnceInit(CMonster* pMonster);

    /**
     * @brief Main processing function for child regeneration
     * Handles automatic spawning of child monsters based on timing
     */
    void OnChildRegenLoop();

    /**
     * @brief Callback when a child monster is created
     * @param pChild Pointer to the newly created child monster
     * @param kind Kind/type of the child monster
     */
    void OnChildMonsterCreate(CMonster* pChild, int kind);

    /**
     * @brief Gets the parent monster
     * @return Pointer to the parent monster, or nullptr if none
     */
    CMonster* GetParent();

    /**
     * @brief Sets the parent monster
     * @param pParent Pointer to the parent monster
     * @return true if parent was set successfully
     */
    bool SetParent(CMonster* pParent);

    /**
     * @brief Gets a child monster by kind and index
     * @param kind Kind/type of child monster (0-2)
     * @param index Index within the kind (0-9)
     * @return Pointer to the child monster, or nullptr if not found
     */
    CMonster* GetChild(int kind, int index);

    /**
     * @brief Adds a child monster to the hierarchy
     * @param kind Kind/type of child monster (0-2)
     * @param pChild Pointer to the child monster
     * @return true if child was added successfully
     */
    bool PushChildMon(int kind, CMonster* pChild);

    /**
     * @brief Removes a specific child monster
     * @param pChild Pointer to the child monster to remove
     * @return true if child was removed successfully
     */
    bool PopChildMon(CMonster* pChild);

    /**
     * @brief Removes all child monsters
     */
    void PopChildMonAll();

    /**
     * @brief Searches for a child monster
     * @param pChild Pointer to the child monster to search for
     * @return true if the child monster was found
     */
    bool SearchChildMon(CMonster* pChild);

    /**
     * @brief Gets the total count of all child monsters
     * @return Total number of child monsters
     */
    uint32_t GetChildCount() const { return m_dwTotalCount; }

    /**
     * @brief Gets the count of child monsters by kind
     * @param kind Kind/type of child monster (0-2)
     * @return Number of child monsters of the specified kind
     */
    uint32_t GetChildCount(int kind) const;

    /**
     * @brief Gets the count of different child kinds
     * @return Number of different child kinds that have monsters
     */
    uint8_t ChildKindCount() const;

    // Getters
    CMonster* GetThisMonster() const { return m_pThisMon; }
    uint32_t GetParentSerial() const { return m_dwParentSerial; }
    uint8_t GetChildMonSetNum() const { return m_byChildMonSetNum; }
    uint32_t GetChildRecallTime() const { return m_dwChildRecallTime; }

    // Setters
    void SetChildMonSetNum(uint8_t setNum) { m_byChildMonSetNum = setNum; }
    void SetChildRecallTime(uint32_t recallTime) { m_dwChildRecallTime = recallTime; }

    /**
     * @brief Validates the current state of the hierarchy
     * @return true if the hierarchy is in a valid state
     */
    bool IsValid() const;

    /**
     * @brief Checks if this monster has any children
     * @return true if there are any child monsters
     */
    bool HasChildren() const { return m_dwTotalCount > 0; }

    /**
     * @brief Checks if this monster is a child of another
     * @return true if this monster has a parent
     */
    bool IsChild() const { return m_pParentMon != nullptr; }

protected:
    /**
     * @brief Spawns child monsters of a specific kind
     * @param kind Kind/type of child monster to spawn
     * @param count Number of children to spawn
     * @return Number of children successfully spawned
     */
    int SpawnChildrenOfKind(int kind, int count);

    /**
     * @brief Finds an empty slot for a child monster
     * @param kind Kind/type of child monster
     * @return Index of empty slot, or -1 if none available
     */
    int FindEmptyChildSlot(int kind) const;

    /**
     * @brief Validates child monster parameters
     * @param kind Kind/type of child monster
     * @param index Index within the kind
     * @return true if parameters are valid
     */
    bool ValidateChildParams(int kind, int index) const;

    /**
     * @brief Updates child monster counts
     */
    void UpdateChildCounts();

    /**
     * @brief Cleans up invalid child references
     */
    void CleanupInvalidChildren();

private:
    // Member variables (maintaining original structure layout where possible)
    CMonsterHierarchyVtbl* vfptr;                                           ///< Virtual function table pointer
    uint32_t m_dwTotalCount;                                                ///< Total count of all child monsters
    CMonster* m_pParentMon;                                                 ///< Pointer to parent monster
    uint32_t m_dwParentSerial;                                              ///< Parent monster serial number
    uint8_t m_byChildMonSetNum;                                             ///< Number of child monster sets
    std::array<std::array<CMonster*, MAX_CHILDREN_PER_KIND>, MAX_CHILD_KINDS> m_pChildMon; ///< Child monster array
    std::array<uint32_t, MAX_CHILD_KINDS> m_dwMonCount;                     ///< Count per child kind
    uint32_t m_dwChildRecallTime;                                           ///< Last child recall time
    CMonster* m_pThisMon;                                                   ///< Pointer to this monster

    // Private helper methods
    void InitializeDefaults();
    void CleanupResources();
    void SetupVirtualFunctionTable();
    bool IsChildSlotValid(int kind, int index) const;
    void RemoveChildFromSlot(int kind, int index);
    void AddChildToSlot(int kind, int index, CMonster* pChild);

    // Disable copy constructor and assignment operator
    CMonsterHierarchy(const CMonsterHierarchy&) = delete;
    CMonsterHierarchy& operator=(const CMonsterHierarchy&) = delete;
};

// Utility functions for monster hierarchy management
namespace CMonsterHierarchyUtils {
    /**
     * @brief Creates a new monster hierarchy instance
     * @return Unique pointer to the created CMonsterHierarchy
     */
    std::unique_ptr<CMonsterHierarchy> CreateHierarchy();

    /**
     * @brief Validates a monster hierarchy configuration
     * @param pHierarchy Pointer to the CMonsterHierarchy to validate
     * @return true if the configuration is valid
     */
    bool ValidateHierarchy(const CMonsterHierarchy* pHierarchy);

    /**
     * @brief Gets the memory footprint of a monster hierarchy
     * @param pHierarchy Pointer to the CMonsterHierarchy
     * @return Size in bytes of the hierarchy's memory usage
     */
    size_t GetMemoryFootprint(const CMonsterHierarchy* pHierarchy);

    /**
     * @brief Counts total monsters in a hierarchy tree
     * @param pHierarchy Pointer to the root CMonsterHierarchy
     * @return Total number of monsters in the hierarchy tree
     */
    int CountHierarchyTree(const CMonsterHierarchy* pHierarchy);

    /**
     * @brief Finds the root monster in a hierarchy
     * @param pHierarchy Pointer to any CMonsterHierarchy in the tree
     * @return Pointer to the root monster
     */
    CMonster* FindRootMonster(const CMonsterHierarchy* pHierarchy);
}

// Legacy C-style interface for compatibility
extern "C" {
    void CMonsterHierarchy_Constructor(CMonsterHierarchy* pThis);
    void CMonsterHierarchy_Destructor(CMonsterHierarchy* pThis);
    void CMonsterHierarchy_Init(CMonsterHierarchy* pThis);
    void CMonsterHierarchy_OnlyOnceInit(CMonsterHierarchy* pThis, CMonster* pMonster);
    void CMonsterHierarchy_OnChildRegenLoop(CMonsterHierarchy* pThis);
    CMonster* CMonsterHierarchy_GetParent(CMonsterHierarchy* pThis);
    bool CMonsterHierarchy_SetParent(CMonsterHierarchy* pThis, CMonster* pParent);
    CMonster* CMonsterHierarchy_GetChild(CMonsterHierarchy* pThis, int kind, int index);
    bool CMonsterHierarchy_PushChildMon(CMonsterHierarchy* pThis, int kind, CMonster* pChild);
    bool CMonsterHierarchy_PopChildMon(CMonsterHierarchy* pThis, CMonster* pChild);
    void CMonsterHierarchy_PopChildMonAll(CMonsterHierarchy* pThis);
    bool CMonsterHierarchy_SearchChildMon(CMonsterHierarchy* pThis, CMonster* pChild);
    uint8_t CMonsterHierarchy_ChildKindCount(CMonsterHierarchy* pThis);
}
