#include "../Headers/CMonsterAggroMgr.h"
#include <cstring>
#include <cmath>
#include <algorithm>
#include <cassert>
#include <iostream>

// Include necessary headers for dependencies
// These would be replaced with actual headers in the real implementation
class CMonster {
public:
    static uint32_t GetAggroShortTime(CMonster* pMonster);
    static uint32_t GetAggroResetTime(CMonster* pMonster);
    static float GetBonusInAreaAggro(CMonster* pMonster);
    
    float m_fCurPos[3];  // Current position
};

class CCharacter {
public:
    uint32_t m_dwObjSerial;  // Object serial number
    bool m_bLive;            // Is alive flag
    bool m_bCorpse;          // Is corpse flag
    float m_fCurPos[3];      // Current position
};

class AggroCaculateData {
public:
    static int GetDefault(AggroCaculateData* pThis, unsigned int type);
};

// Global utility functions
extern uint32_t GetLoopTime();
extern float GetSqrt(const float* pos1, const float* pos2);

// Global aggro calculate data instance
extern AggroCaculateData g_AggroCaculateData;

// CAggroNode implementation

CAggroNode::CAggroNode()
    : m_pCharacter(nullptr)
    , m_dwObjectSerial(0xFFFFFFFF)
    , m_nAggroData(0)
    , m_nDamageData(0)
    , m_nKingPowerDamage(0)
{
}

void CAggroNode::Init()
{
    m_pCharacter = nullptr;
    m_dwObjectSerial = 0xFFFFFFFF;
    m_nAggroData = 0;
    m_nDamageData = 0;
    m_nKingPowerDamage = 0;
}

void CAggroNode::Set(CCharacter* pCharacter)
{
    Init();
    if (pCharacter) {
        m_pCharacter = pCharacter;
        m_dwObjectSerial = pCharacter->m_dwObjSerial;
    }
}

bool CAggroNode::IsLive() const
{
    return m_pCharacter != nullptr && 
           m_pCharacter->m_bLive && 
           m_pCharacter->m_dwObjSerial == m_dwObjectSerial;
}

// CMonsterAggroMgr implementation

CMonsterAggroMgr::CMonsterAggroMgr()
    : m_pTopAggroCharacter(nullptr)
    , m_pTopDamageCharacter(nullptr)
    , m_pKingPowerDamageCharacter(nullptr)
    , m_dwAggroCount(0)
    , m_dwAllResetLastTime(0)
    , m_dwShortRankLastTime(0)
    , m_dwAllResetTimer(DEFAULT_ALL_RESET_TIMER)
    , m_dwShortRankTimer(DEFAULT_SHORT_RANK_TIMER)
    , m_pMonster(nullptr)
{
    InitializeDefaults();
}

CMonsterAggroMgr::~CMonsterAggroMgr()
{
    CleanupResources();
}

void CMonsterAggroMgr::InitializeDefaults()
{
    // Initialize all aggro nodes
    for (auto& node : m_AggroPool) {
        node.Init();
    }
    
    m_pTopAggroCharacter = nullptr;
    m_pTopDamageCharacter = nullptr;
    m_pKingPowerDamageCharacter = nullptr;
    m_dwAggroCount = 0;
}

void CMonsterAggroMgr::CleanupResources()
{
    // Reset all pointers
    m_pTopAggroCharacter = nullptr;
    m_pTopDamageCharacter = nullptr;
    m_pKingPowerDamageCharacter = nullptr;
    m_pMonster = nullptr;
}

void CMonsterAggroMgr::Init()
{
    // Initialize all aggro nodes
    for (auto& node : m_AggroPool) {
        node.Init();
    }
    
    m_dwAggroCount = 0;
    m_dwAllResetLastTime = 0;
    m_dwShortRankLastTime = 0;
    
    // Set timers based on monster properties
    if (m_pMonster) {
        m_dwShortRankTimer = CMonster::GetAggroShortTime(m_pMonster);
        m_dwAllResetTimer = CMonster::GetAggroResetTime(m_pMonster);
    }
    
    m_pTopDamageCharacter = nullptr;
    m_pTopAggroCharacter = nullptr;
    m_pKingPowerDamageCharacter = nullptr;
}

void CMonsterAggroMgr::OnlyOnceInit(CMonster* pMonster)
{
    m_pMonster = pMonster;
    m_dwAllResetTimer = DEFAULT_ALL_RESET_TIMER;
    m_dwShortRankTimer = DEFAULT_SHORT_RANK_TIMER;
}

void CMonsterAggroMgr::Process()
{
    uint32_t currentTime = GetLoopTime();
    
    if (m_dwAggroCount == 0) {
        return;
    }
    
    // Check for full aggro reset
    if (currentTime - m_dwAllResetLastTime >= m_dwAllResetTimer) {
        Init();
        m_dwAllResetLastTime = currentTime;
        return;
    }
    
    // Check for short ranking update
    if (static_cast<int>(currentTime - m_dwShortRankLastTime) >= static_cast<int>(m_dwShortRankTimer)) {
        _ShortRank();
        SendChangeAggroData();
        m_dwShortRankLastTime = currentTime;
    }
}

void CMonsterAggroMgr::SetAggro(CCharacter* pCharacter, int damage, int attackType, 
                                uint32_t attackSerial, int otherPlayerSupport, int tempSkill)
{
    if (!ValidateCharacter(pCharacter)) {
        return;
    }
    
    // Search for existing aggro node
    CAggroNode* pNode = _SearchAggroNode(pCharacter);
    
    // If not found, get a blank node
    if (!pNode) {
        pNode = _GetBlinkNode();
        if (!pNode) {
            return; // No available nodes
        }
        
        pNode->Set(pCharacter);
        m_dwAggroCount++;
    }
    
    // Calculate aggro bonus
    int aggroBonus = CalculateAggroBonus(pCharacter);
    
    // Update aggro data
    pNode->m_nDamageData += damage;
    pNode->m_nAggroData += aggroBonus;
    
    // Handle king power damage
    if (attackType == 1) { // Assuming 1 is king power attack
        pNode->m_nKingPowerDamage += damage;
    }
}

void CMonsterAggroMgr::ResetAggro()
{
    uint32_t currentTime = GetLoopTime();
    m_dwShortRankLastTime = currentTime + m_dwShortRankTimer;
}

void CMonsterAggroMgr::ShortRankDelay(uint32_t delayTime)
{
    uint32_t currentTime = GetLoopTime();
    m_dwShortRankLastTime = currentTime + delayTime;
}

CAggroNode* CMonsterAggroMgr::SearchAggroNode(CCharacter* pCharacter)
{
    return _SearchAggroNode(pCharacter);
}

CCharacter* CMonsterAggroMgr::GetTopAggroCharacter()
{
    if (m_pTopAggroCharacter && 
        m_pTopAggroCharacter->m_bLive && 
        !m_pTopAggroCharacter->m_bCorpse) {
        return m_pTopAggroCharacter;
    }
    
    m_pTopAggroCharacter = nullptr;
    return nullptr;
}

CCharacter* CMonsterAggroMgr::GetTopDamageCharacter()
{
    if (m_pTopDamageCharacter && 
        m_pTopDamageCharacter->m_bLive && 
        !m_pTopDamageCharacter->m_bCorpse) {
        return m_pTopDamageCharacter;
    }
    
    m_pTopDamageCharacter = nullptr;
    return nullptr;
}

CCharacter* CMonsterAggroMgr::GetKingPowerDamageCharacter()
{
    if (m_pKingPowerDamageCharacter && 
        m_pKingPowerDamageCharacter->m_bLive && 
        !m_pKingPowerDamageCharacter->m_bCorpse) {
        return m_pKingPowerDamageCharacter;
    }
    
    m_pKingPowerDamageCharacter = nullptr;
    return nullptr;
}

bool CMonsterAggroMgr::IsValid() const
{
    return m_pMonster != nullptr;
}

void CMonsterAggroMgr::_ShortRank()
{
    CCharacter* previousTopAggro = m_pTopAggroCharacter;
    
    // Reset top characters
    m_pTopDamageCharacter = nullptr;
    m_pTopAggroCharacter = nullptr;
    m_pKingPowerDamageCharacter = nullptr;
    
    int maxAggro = -1000;
    int maxDamage = 0;
    int maxKingPowerDamage = 0;
    float minDistance = MAX_AGGRO_DISTANCE;
    int closestAggroIndex = INVALID_AGGRO_INDEX;
    
    // First pass: find top damage and king power damage
    for (int i = 0; i < MAX_AGGRO_NODES; ++i) {
        if (!m_AggroPool[i].IsLive()) {
            continue;
        }
        
        if (m_AggroPool[i].m_nDamageData > maxDamage) {
            maxDamage = m_AggroPool[i].m_nDamageData;
            m_pTopDamageCharacter = m_AggroPool[i].m_pCharacter;
        }
        
        if (m_AggroPool[i].m_nKingPowerDamage > maxKingPowerDamage) {
            maxKingPowerDamage = m_AggroPool[i].m_nKingPowerDamage;
            m_pKingPowerDamageCharacter = m_AggroPool[i].m_pCharacter;
        }
        
        // Find closest character for distance-based aggro bonus
        if (m_pMonster) {
            float distance = GetSqrt(m_pMonster->m_fCurPos, m_AggroPool[i].m_pCharacter->m_fCurPos);
            if (distance < minDistance) {
                minDistance = distance;
                closestAggroIndex = i;
            }
        }
    }
    
    // Second pass: apply distance and area bonuses
    for (int i = 0; i < MAX_AGGRO_NODES; ++i) {
        if (!m_AggroPool[i].IsLive()) {
            continue;
        }
        
        // Apply area aggro bonus
        if (m_pMonster) {
            float distance = GetSqrt(m_pMonster->m_fCurPos, m_AggroPool[i].m_pCharacter->m_fCurPos);
            if (distance <= MAX_AGGRO_DISTANCE) {
                int areaBonus = AggroCaculateData::GetDefault(&g_AggroCaculateData, 8);
                m_AggroPool[i].m_nAggroData += areaBonus;
            }
        }
        
        // Apply closest character bonus
        if (closestAggroIndex == i) {
            int closestBonus = AggroCaculateData::GetDefault(&g_AggroCaculateData, 12);
            m_AggroPool[i].m_nAggroData += closestBonus;
        }
        
        // Find top aggro character
        if (m_AggroPool[i].m_nAggroData > maxAggro) {
            maxAggro = m_AggroPool[i].m_nAggroData;
            m_pTopAggroCharacter = m_AggroPool[i].m_pCharacter;
        }
    }
}

CAggroNode* CMonsterAggroMgr::_GetBlinkNode()
{
    for (auto& node : m_AggroPool) {
        if (!node.IsLive()) {
            return &node;
        }
    }
    return nullptr;
}

CAggroNode* CMonsterAggroMgr::_SearchAggroNode(CCharacter* pCharacter)
{
    if (!pCharacter) {
        return nullptr;
    }
    
    for (auto& node : m_AggroPool) {
        if (node.m_pCharacter == pCharacter &&
            node.m_dwObjectSerial == pCharacter->m_dwObjSerial &&
            node.IsLive()) {
            return &node;
        }
    }
    
    return nullptr;
}

void CMonsterAggroMgr::SendChangeAggroData()
{
    // Implementation would send network messages about aggro changes
    // This is a placeholder for the actual network communication
}

bool CMonsterAggroMgr::ValidateCharacter(CCharacter* pCharacter) const
{
    return pCharacter != nullptr && pCharacter->m_bLive && !pCharacter->m_bCorpse;
}

int CMonsterAggroMgr::CalculateAggroBonus(CCharacter* pCharacter) const
{
    if (!pCharacter || !m_pMonster) {
        return 0;
    }
    
    // Calculate distance-based aggro bonus
    float distance = GetSqrt(m_pMonster->m_fCurPos, pCharacter->m_fCurPos);
    if (distance > MAX_AGGRO_DISTANCE) {
        return 0;
    }
    
    // Get area bonus from monster
    float areaBonus = CMonster::GetBonusInAreaAggro(m_pMonster);
    
    // Calculate final aggro bonus
    return static_cast<int>(areaBonus * (MAX_AGGRO_DISTANCE - distance) / MAX_AGGRO_DISTANCE);
}

void CMonsterAggroMgr::UpdateAggroCount()
{
    m_dwAggroCount = 0;
    for (const auto& node : m_AggroPool) {
        if (node.IsLive()) {
            m_dwAggroCount++;
        }
    }
}

bool CMonsterAggroMgr::IsCharacterValid(CCharacter* pCharacter) const
{
    return ValidateCharacter(pCharacter);
}

void CMonsterAggroMgr::ProcessAggroDecay()
{
    // Implementation for aggro decay over time
    // This would reduce aggro values gradually
}

void CMonsterAggroMgr::ProcessRankingUpdates()
{
    // Implementation for periodic ranking updates
    // This would be called during Process()
}

// CMonsterAggroMgrUtils implementation

namespace CMonsterAggroMgrUtils {

std::unique_ptr<CMonsterAggroMgr> CreateAggroManager()
{
    return std::make_unique<CMonsterAggroMgr>();
}

bool ValidateAggroManager(const CMonsterAggroMgr* pMgr)
{
    return pMgr != nullptr && pMgr->IsValid();
}

size_t GetMemoryFootprint(const CMonsterAggroMgr* pMgr)
{
    if (!pMgr) {
        return 0;
    }
    
    // Calculate approximate memory usage
    size_t footprint = sizeof(CMonsterAggroMgr);
    footprint += pMgr->GetAggroPool().size() * sizeof(CAggroNode);
    
    return footprint;
}

int CalculateAggroPriority(int damage, int attackType, float distance)
{
    int priority = damage;
    
    // Apply attack type modifier
    if (attackType == 1) { // King power attack
        priority *= 2;
    }
    
    // Apply distance modifier
    if (distance < CMonsterAggroMgr::MAX_AGGRO_DISTANCE) {
        priority = static_cast<int>(priority * (CMonsterAggroMgr::MAX_AGGRO_DISTANCE - distance) / CMonsterAggroMgr::MAX_AGGRO_DISTANCE);
    } else {
        priority = 0;
    }
    
    return priority;
}

} // namespace CMonsterAggroMgrUtils

// Legacy C-style interface implementation

extern "C" {

void CMonsterAggroMgr_Constructor(CMonsterAggroMgr* pThis)
{
    if (pThis) {
        new (pThis) CMonsterAggroMgr();
    }
}

void CMonsterAggroMgr_Destructor(CMonsterAggroMgr* pThis)
{
    if (pThis) {
        pThis->~CMonsterAggroMgr();
    }
}

void CMonsterAggroMgr_Init(CMonsterAggroMgr* pThis)
{
    if (pThis) {
        pThis->Init();
    }
}

void CMonsterAggroMgr_OnlyOnceInit(CMonsterAggroMgr* pThis, CMonster* pMonster)
{
    if (pThis) {
        pThis->OnlyOnceInit(pMonster);
    }
}

void CMonsterAggroMgr_Process(CMonsterAggroMgr* pThis)
{
    if (pThis) {
        pThis->Process();
    }
}

void CMonsterAggroMgr_SetAggro(CMonsterAggroMgr* pThis, CCharacter* pCharacter, 
                               int damage, int attackType, uint32_t attackSerial, 
                               int otherPlayerSupport, int tempSkill)
{
    if (pThis) {
        pThis->SetAggro(pCharacter, damage, attackType, attackSerial, otherPlayerSupport, tempSkill);
    }
}

void CMonsterAggroMgr_ResetAggro(CMonsterAggroMgr* pThis)
{
    if (pThis) {
        pThis->ResetAggro();
    }
}

CCharacter* CMonsterAggroMgr_GetTopAggroCharacter(CMonsterAggroMgr* pThis)
{
    return pThis ? pThis->GetTopAggroCharacter() : nullptr;
}

CCharacter* CMonsterAggroMgr_GetTopDamageCharacter(CMonsterAggroMgr* pThis)
{
    return pThis ? pThis->GetTopDamageCharacter() : nullptr;
}

CAggroNode* CMonsterAggroMgr_SearchAggroNode(CMonsterAggroMgr* pThis, CCharacter* pCharacter)
{
    return pThis ? pThis->SearchAggroNode(pCharacter) : nullptr;
}

} // extern "C"
