# D3DUtil Module

## Overview

The D3DUtil module provides DirectX utility functions for matrix and vector operations, particularly for cube map view matrix generation. This module has been refactored from the original decompiled C source code to modern C++17/C++20 standards.

## Original Source

- **Original Function**: `D3DUtil_GetCubeMapViewMatrix`
- **Original Address**: `0x14052B5D0`
- **Original File**: `D3DUtil_GetCubeMapViewMatrixYAAUD3DXMATRIXKZ_14052B5D0.c`

## Files

- **Header**: `NexusProtection/world/Headers/D3DUtil.h`
- **Source**: `NexusProtection/world/Source/D3DUtil.cpp`
- **Documentation**: `NexusProtection/world/Documents/D3DUtil_README.md`

## Key Features

### Modern C++ Implementation
- Uses C++17/C++20 features
- Type-safe enumerations
- RAII and exception safety
- Const-correctness
- Modern naming conventions

### Cube Map Support
- Support for all 6 cube map faces
- Type-safe face enumeration
- Validation of face indices
- Descriptive face naming for debugging

### Matrix Operations
- Left-handed coordinate system support
- Proper matrix construction and manipulation
- Vector cross product and normalization
- Look-at matrix generation

## API Reference

### Enumerations

```cpp
enum class CubeMapFace : uint32_t {
    POSITIVE_X = 0,  // Right face
    NEGATIVE_X = 1,  // Left face
    POSITIVE_Y = 2,  // Top face
    NEGATIVE_Y = 3,  // Bottom face
    POSITIVE_Z = 4,  // Front face
    NEGATIVE_Z = 5   // Back face
};
```

### Core Functions

#### GetCubeMapViewMatrix
```cpp
D3DXMATRIX GetCubeMapViewMatrix(uint32_t faceIndex);
D3DXMATRIX GetCubeMapViewMatrix(CubeMapFace face);
```
Generates a view matrix for cube map rendering for the specified face.

#### MatrixLookAtLH
```cpp
D3DXMATRIX MatrixLookAtLH(const D3DXVECTOR3& eye, 
                          const D3DXVECTOR3& at, 
                          const D3DXVECTOR3& up);
```
Creates a left-handed look-at matrix.

### Utility Functions

#### IsValidCubeMapFace
```cpp
bool IsValidCubeMapFace(uint32_t faceIndex);
```
Validates a cube map face index (0-5).

#### CubeMapFaceToIndex / IndexToCubeMapFace
```cpp
uint32_t CubeMapFaceToIndex(CubeMapFace face);
CubeMapFace IndexToCubeMapFace(uint32_t faceIndex);
```
Conversion functions between face enums and indices.

#### GetCubeMapFaceName
```cpp
const char* GetCubeMapFaceName(CubeMapFace face);
```
Returns a descriptive name for debugging purposes.

## Usage Examples

### Basic Cube Map View Matrix Generation
```cpp
#include "D3DUtil.h"

// Generate view matrix for positive X face
D3DXMATRIX viewMatrix = D3DUtil::GetCubeMapViewMatrix(D3DUtil::CubeMapFace::POSITIVE_X);

// Or using index
D3DXMATRIX viewMatrix2 = D3DUtil::GetCubeMapViewMatrix(0);
```

### Custom Look-At Matrix
```cpp
D3DXVECTOR3 eye(0.0f, 0.0f, 0.0f);
D3DXVECTOR3 target(1.0f, 0.0f, 0.0f);
D3DXVECTOR3 up(0.0f, 1.0f, 0.0f);

D3DXMATRIX lookAtMatrix = D3DUtil::MatrixLookAtLH(eye, target, up);
```

### Face Validation and Debugging
```cpp
uint32_t faceIndex = 2;
if (D3DUtil::IsValidCubeMapFace(faceIndex)) {
    auto face = D3DUtil::IndexToCubeMapFace(faceIndex);
    const char* faceName = D3DUtil::GetCubeMapFaceName(face);
    // faceName will be "POSITIVE_Y"
}
```

## Legacy Compatibility

The module provides C-style interfaces for compatibility with existing code:

```cpp
extern "C" {
    D3DXMATRIX* D3DUtil_GetCubeMapViewMatrix(D3DXMATRIX* retstr, int faceIndex);
    D3DXMATRIX* D3DXMatrixLookAtLH_0(D3DXMATRIX* result, 
                                     const int* eye, 
                                     const char* at, 
                                     const float* up);
}
```

## Refactoring Notes

### Changes from Original
1. **Type Safety**: Replaced raw integers with type-safe enumerations
2. **Error Handling**: Added proper validation and exception handling
3. **Memory Safety**: Eliminated raw pointer arithmetic where possible
4. **Const Correctness**: Added const qualifiers throughout
5. **Documentation**: Comprehensive inline documentation
6. **Naming**: Modern C++ naming conventions

### Preserved Behavior
- Identical mathematical operations
- Same coordinate system (left-handed)
- Compatible matrix layout
- Preserved original face orientations

## Testing Recommendations

1. **Unit Tests**: Test each cube map face orientation
2. **Matrix Validation**: Verify orthogonality and determinant
3. **Legacy Compatibility**: Test C-style interface compatibility
4. **Edge Cases**: Test invalid face indices and null pointers
5. **Performance**: Benchmark against original implementation

## Integration Notes

- Compatible with Visual Studio 2022 (v143 toolset)
- Requires C++17 or later
- No external dependencies beyond standard library
- Thread-safe (all functions are stateless)
- Exception-safe with strong guarantee

## Future Enhancements

1. **SIMD Optimization**: Consider vectorized matrix operations
2. **Template Support**: Generic matrix/vector templates
3. **Additional Projections**: Perspective and orthographic matrices
4. **Quaternion Support**: Rotation quaternion utilities
5. **Validation Modes**: Debug vs. release validation levels
