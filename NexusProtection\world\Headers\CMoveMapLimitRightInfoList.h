#pragma once

/**
 * @file CMoveMapLimitRightInfoList.h
 * @brief Container for managing map movement limitation rights information
 * @details Container class that manages a list of CMoveMapLimitRightInfo objects
 * <AUTHOR> Development Team
 * @date 2024
 */

#include <vector>
#include <memory>

// Forward declarations
class CMoveMapLimitRightInfo;

/**
 * @class CMoveMapLimitRightInfoList
 * @brief Container for managing map movement limitation rights information
 * 
 * This class manages a collection of CMoveMapLimitRightInfo objects that define
 * user rights and permissions for map movement limitations. It provides functionality to:
 * - Store and manage rights information objects
 * - Retrieve rights information by index
 * - Manage the lifecycle of rights objects
 * - Provide container-like operations for rights management
 */
class CMoveMapLimitRightInfoList
{
public:
    // Constructor and destructor
    CMoveMapLimitRightInfoList();
    ~CMoveMapLimitRightInfoList();

    // Core functionality
    CMoveMapLimitRightInfo* Get(int iIndex);
    const CMoveMapLimitRightInfo* Get(int iIndex) const;

    // Container operations
    void Add(const CMoveMapLimitRightInfo& rightInfo);
    void Clear();
    bool Remove(int iIndex);
    
    // Information retrieval
    size_t Size() const;
    bool Empty() const;
    bool IsValidIndex(int iIndex) const;

    // Iterator support
    std::vector<CMoveMapLimitRightInfo>::iterator begin();
    std::vector<CMoveMapLimitRightInfo>::iterator end();
    std::vector<CMoveMapLimitRightInfo>::const_iterator begin() const;
    std::vector<CMoveMapLimitRightInfo>::const_iterator end() const;

    // Capacity management
    void Reserve(size_t capacity);
    size_t Capacity() const;

    // Search operations
    CMoveMapLimitRightInfo* Find(int iRightType);
    const CMoveMapLimitRightInfo* Find(int iRightType) const;

private:
    // Prevent copying
    CMoveMapLimitRightInfoList(const CMoveMapLimitRightInfoList&) = delete;
    CMoveMapLimitRightInfoList& operator=(const CMoveMapLimitRightInfoList&) = delete;

    // Helper methods
    void InitializeContainer();

private:
    // Member variables
    std::vector<CMoveMapLimitRightInfo> m_vecRight;  ///< Vector of rights information objects
};
