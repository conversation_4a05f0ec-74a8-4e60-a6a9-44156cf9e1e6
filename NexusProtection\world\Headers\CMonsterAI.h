/*
 * CMonsterAI.h - Monster AI System Handler
 * Refactored for Visual Studio 2022 compatibility
 * Original: 0CMonsterAIQEAAXZ_14014F950.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <array>
#include <vector>

// Forward declarations
class Us_HFSM;
class CMonster;

/**
 * SF_Timer class for special function timing
 */
class SF_Timer {
public:
    SF_Timer();
    ~SF_Timer();

    void Initialize();
    void Reset();
    void Start();
    void Stop();
    void Update();

    bool IsActive() const { return m_bActive; }
    uint32_t GetElapsedTime() const { return m_dwElapsedTime; }

private:
    bool m_bActive;
    uint32_t m_dwStartTime;
    uint32_t m_dwElapsedTime;
    uint32_t m_dwDuration;
};

/**
 * CPathMgr (Path Manager) class for pathfinding
 */
class CPathMgr {
public:
    CPathMgr();
    ~CPathMgr();

    void Initialize();
    void Reset();
    void FindPath(const float* startPos, const float* endPos);
    bool HasValidPath() const { return m_bHasPath; }

private:
    bool m_bHasPath;
    std::vector<float> m_pathPoints;
    int m_nCurrentWaypoint;
};

// Virtual function table structure
struct Us_HFSMVtbl {
    void* functions[16]; // Placeholder for virtual function pointers
};

/**
 * Monster AI System Handler
 * Handles monster artificial intelligence initialization and management
 */
class CMonsterAI {
public:
    // Constructor/Destructor
    CMonsterAI();
    virtual ~CMonsterAI();

    // Core AI functionality
    void InitializeAI();
    void InitializeStateMachine();
    void InitializePathfinding();
    void InitializeTimers();
    void InitializeAssistSystem();
    
    // State machine management
    void SetupHierarchicalStateMachine();
    void ConfigureVirtualFunctionTable();
    void InitializeStateTransitions();
    
    // Pathfinding system
    void SetupPathfinder();
    void ResetPathfindingCounters();
    void ConfigurePathfindingParameters();
    
    // Timer system management
    void InitializeSFTimers();
    void SetupTimerArray();
    void ConfigureTimerParameters();
    
    // Assist monster system
    void InitializeAssistMonster();
    void ResetAssistPointer();
    void ConfigureAssistBehavior();
    
    // Validation and error handling
    bool ValidateAIState() const;
    bool ValidatePathfinder() const;
    bool ValidateTimers() const;
    
    // Logging and debugging
    void LogAIInitialization() const;
    void LogStateTransition(const char* fromState, const char* toState) const;
    void LogPathfindingActivity(const char* activity) const;
    void LogTimerActivity(const char* timerName, const char* activity) const;
    
    // Getters and setters
    CPathMgr* GetPathFinder() { return m_pPathFinder; }
    const CPathMgr* GetPathFinder() const { return m_pPathFinder; }
    
    CMonster* GetAssistMonster() { return m_pAsistMonster; }
    const CMonster* GetAssistMonster() const { return m_pAsistMonster; }
    void SetAssistMonster(CMonster* pMonster) { m_pAsistMonster = pMonster; }
    
    int GetPathFindFailCount() const { return m_nCurPathFindFailCount; }
    void SetPathFindFailCount(int count) { m_nCurPathFindFailCount = count; }
    void IncrementPathFindFailCount() { ++m_nCurPathFindFailCount; }
    void ResetPathFindFailCount() { m_nCurPathFindFailCount = 0; }

private:
    // Internal data members (equivalent to original structure)
    Us_HFSMVtbl* vfptr;                    // Virtual function table pointer
    SF_Timer* m_SFCheckTime;               // SF Timer array (8 elements, 4 bytes each)
    CPathMgr* m_pPathFinder;               // Path finding manager pointer
    CMonster* m_pAsistMonster;             // Assist monster pointer
    int m_nCurPathFindFailCount;           // Current pathfinding failure count
    
    // Internal processing helpers
    void InitializeProcessingContext();
    void CleanupProcessingContext();
    void SetupInternalStructures();
    void ConfigureDefaultParameters();
    
    // Constants
    static constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    static constexpr size_t STACK_INIT_SIZE = 12 * sizeof(uint32_t);
    static constexpr size_t SF_TIMER_COUNT = 8;
    static constexpr size_t SF_TIMER_SIZE = 4;
    static constexpr int64_t INIT_MARKER = -2;
    static constexpr int DEFAULT_PATHFIND_FAIL_COUNT = 0;
    
    // Disable copy constructor and assignment operator
    CMonsterAI(const CMonsterAI&) = delete;
    CMonsterAI& operator=(const CMonsterAI&) = delete;
};

/**
 * Us_HFSM (Hierarchical Finite State Machine) base class
 */
class Us_HFSM {
public:
    Us_HFSM();
    virtual ~Us_HFSM();

    virtual void Initialize();
    virtual void Update();
    virtual void Cleanup();

    // State management
    virtual void ChangeState(int newState);
    virtual int GetCurrentState() const { return m_nCurrentState; }

protected:
    Us_HFSMVtbl* vfptr;  // Virtual function table
    int m_nCurrentState;
    int m_nPreviousState;

private:
    void SetupVirtualTable();
};

/**
 * Legacy C-style interface for compatibility
 * These functions maintain the original calling convention for existing code
 */
namespace CMonsterAILegacy {
    // Original constructor signature for compatibility
    void CMonsterAI_Constructor(CMonsterAI* pThis);
    
    // Helper functions for legacy integration
    void InitializeLegacySupport();
    void CleanupLegacySupport();
}

/**
 * Utility functions for monster AI management
 */
namespace CMonsterAIUtils {
    // Validation utilities
    bool IsValidAI(const CMonsterAI* pAI);
    bool IsValidPathfinder(const CPathMgr* pPathMgr);
    bool IsValidTimer(const SF_Timer* pTimer);
    
    // String utilities
    std::string SafeStringCopy(const char* source, size_t maxLength = 256);
    std::string FormatAIInfo(const CMonsterAI* pAI);
    std::string FormatPathfinderInfo(const CPathMgr* pPathMgr);
    
    // Timer utilities
    void InitializeTimerArray(SF_Timer* timers, size_t count);
    void ResetTimerArray(SF_Timer* timers, size_t count);
    void UpdateTimerArray(SF_Timer* timers, size_t count);
    
    // Logging utilities
    void LogAICall(const char* functionName, const CMonsterAI* pAI, const char* details = nullptr);
    void LogError(const char* errorMessage, const char* context = nullptr);
    void LogAIOperation(const char* operation, const CMonsterAI* pAI, bool success);
    void LogPathfindingOperation(const char* operation, const CPathMgr* pPathMgr, bool success);
    void LogTimerOperation(const char* operation, const SF_Timer* pTimer, bool success);
}

/**
 * Monster AI context structure
 */
struct MonsterAIContext {
    const CMonsterAI* ai;
    std::string aiType;
    bool initialized;
    bool pathfinderReady;
    bool timersActive;
    std::string initializationLog;
    
    MonsterAIContext() : ai(nullptr), initialized(false), pathfinderReady(false), timersActive(false) {}
};

/**
 * AI initialization result enumeration
 */
enum class AIInitResult {
    SUCCESS = 0,
    INVALID_AI = -1,
    HFSM_INIT_FAILED = -2,
    PATHFINDER_INIT_FAILED = -3,
    TIMER_INIT_FAILED = -4,
    VTABLE_SETUP_FAILED = -5,
    UNKNOWN_ERROR = -6
};

// Type definitions for better code clarity
using AIInitHandler = bool(*)(CMonsterAI*);
using StateChangeCallback = void(*)(int, int);
using PathfindingCallback = void(*)(const float*, const float*);
using TimerCallback = void(*)(SF_Timer*);
using ErrorCallback = void(*)(const char*, const CMonsterAI*);

// Constants for monster AI processing
namespace CMonsterAIConstants {
    constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    constexpr size_t STACK_INIT_SIZE = 12 * sizeof(uint32_t);
    constexpr size_t SF_TIMER_COUNT = 8;
    constexpr size_t SF_TIMER_SIZE = 4;
    constexpr int64_t INIT_MARKER = -2;
    constexpr int DEFAULT_PATHFIND_FAIL_COUNT = 0;
    
    // AI states
    constexpr int AI_STATE_IDLE = 0;
    constexpr int AI_STATE_PATROL = 1;
    constexpr int AI_STATE_CHASE = 2;
    constexpr int AI_STATE_ATTACK = 3;
    constexpr int AI_STATE_RETURN = 4;
    
    // Pathfinding constants
    constexpr int MAX_PATHFIND_FAILURES = 5;
    constexpr float PATHFIND_TOLERANCE = 1.0f;
    
    // Timer constants
    constexpr uint32_t DEFAULT_TIMER_DURATION = 1000; // 1 second in milliseconds
    constexpr uint32_t MAX_TIMER_DURATION = 60000;    // 1 minute in milliseconds
}
