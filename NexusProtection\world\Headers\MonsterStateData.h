#pragma once

#include <cstdint>
#include <memory>
#include <string>

/**
 * @class MonsterStateData
 * @brief Manages monster state information in a compact format
 * 
 * This class encapsulates monster state information in a compact 16-bit
 * chunk format, with different bits representing different state aspects.
 * It provides methods for getting and setting state data, as well as
 * comparison operations.
 * 
 * Bit Layout:
 * - Bit 0: Move Type (0 = normal, 1 = combat)
 * - Bit 1: Speed Effect (0 = normal, 1 = speed effect active)
 * - Bits 2-4: Emotion State (0-7)
 * - Bit 6: Combat State (0 = normal, 1 = combat)
 * - Other bits: Reserved for future use
 * 
 * @note Refactored from decompiled MonsterStateData structure
 */
class MonsterStateData {
public:
    // Constants for bit manipulation
    static constexpr uint16_t MOVE_TYPE_MASK = 0x0001;      // Bit 0
    static constexpr uint16_t SPEED_EFFECT_MASK = 0x0002;   // Bit 1
    static constexpr uint16_t EMOTION_STATE_MASK = 0x001C;  // Bits 2-4
    static constexpr uint16_t EMOTION_STATE_SHIFT = 2;
    static constexpr uint16_t COMBAT_STATE_MASK = 0x0040;   // Bit 6
    static constexpr uint16_t COMBAT_STATE_SHIFT = 6;
    static constexpr uint16_t RESERVED_BITS_MASK = 0xFFA0;  // All other bits

    /**
     * @brief Default constructor
     * Initializes state data to zero
     */
    MonsterStateData();

    /**
     * @brief Copy constructor
     * @param other MonsterStateData to copy from
     */
    MonsterStateData(const MonsterStateData& other);

    /**
     * @brief Destructor
     */
    ~MonsterStateData();

    /**
     * @brief Gets the raw state chunk data
     * @return 16-bit state chunk
     */
    uint16_t GetStateChunk() const;

    /**
     * @brief Sets the raw state chunk data
     * @param stateChunk 16-bit state chunk
     */
    void SetStateChunk(uint16_t stateChunk);

    /**
     * @brief Gets the move type
     * @return 0 for normal movement, 1 for combat movement
     */
    uint8_t GetMoveType() const;

    /**
     * @brief Sets the move type
     * @param moveType 0 for normal movement, 1 for combat movement
     */
    void SetMoveType(uint8_t moveType);

    /**
     * @brief Gets the speed effect state
     * @return true if speed effect is active
     */
    bool GetSpeedEffect() const;

    /**
     * @brief Sets the speed effect state
     * @param hasEffect true to activate speed effect
     */
    void SetSpeedEffect(bool hasEffect);

    /**
     * @brief Gets the emotion state
     * @return Emotion state value (0-7)
     */
    uint8_t GetEmotionState() const;

    /**
     * @brief Sets the emotion state
     * @param emotionState Emotion state value (0-7)
     */
    void SetEmotionState(uint8_t emotionState);

    /**
     * @brief Gets the combat state
     * @return 0 for normal state, 1 for combat state
     */
    uint8_t GetCombatState() const;

    /**
     * @brief Sets the combat state
     * @param combatState 0 for normal state, 1 for combat state
     */
    void SetCombatState(uint8_t combatState);

    /**
     * @brief Clears all state data
     */
    void Clear();

    /**
     * @brief Equality comparison operator
     * @param rhs Right-hand side MonsterStateData
     * @return true if states are equal
     */
    bool operator==(const MonsterStateData& rhs) const;

    /**
     * @brief Inequality comparison operator
     * @param rhs Right-hand side MonsterStateData
     * @return true if states are not equal
     */
    bool operator!=(const MonsterStateData& rhs) const;

    /**
     * @brief Assignment operator
     * @param rhs Right-hand side MonsterStateData
     * @return Reference to this MonsterStateData
     */
    MonsterStateData& operator=(const MonsterStateData& rhs);

    /**
     * @brief Validates the state data
     * @return true if the state data is valid
     */
    bool IsValid() const;

    /**
     * @brief Creates a string representation of the state
     * @return String describing the current state
     */
    std::string ToString() const;

private:
    uint16_t m_wSendChunkData;  ///< Compact state data chunk

    // Private helper methods
    void ValidateBits();
    uint8_t ExtractBits(uint16_t mask, uint8_t shift) const;
    void SetBits(uint16_t mask, uint8_t shift, uint8_t value);
};

// Utility functions for monster state data management
namespace MonsterStateDataUtils {
    /**
     * @brief Creates a new monster state data instance
     * @return Unique pointer to the created MonsterStateData
     */
    std::unique_ptr<MonsterStateData> CreateStateData();

    /**
     * @brief Creates a state data with specific settings
     * @param moveType Move type value
     * @param emotionState Emotion state value
     * @param combatState Combat state value
     * @param speedEffect Speed effect state
     * @return Unique pointer to the configured MonsterStateData
     */
    std::unique_ptr<MonsterStateData> CreateConfiguredStateData(
        uint8_t moveType, 
        uint8_t emotionState, 
        uint8_t combatState, 
        bool speedEffect
    );

    /**
     * @brief Validates a monster state data configuration
     * @param pStateData Pointer to the MonsterStateData to validate
     * @return true if the configuration is valid
     */
    bool ValidateStateData(const MonsterStateData* pStateData);

    /**
     * @brief Compares two state data objects for differences
     * @param state1 First state data
     * @param state2 Second state data
     * @return String describing the differences, or empty if identical
     */
    std::string CompareStates(const MonsterStateData& state1, const MonsterStateData& state2);
}

// Legacy C-style interface for compatibility
extern "C" {
    void MonsterStateData_Constructor(MonsterStateData* pThis);
    void MonsterStateData_Destructor(MonsterStateData* pThis);
    uint16_t MonsterStateData_GetStateChunk(MonsterStateData* pThis);
    void MonsterStateData_SetStateChunk(MonsterStateData* pThis, uint16_t stateChunk);
    uint8_t MonsterStateData_GetMoveType(MonsterStateData* pThis);
    void MonsterStateData_SetMoveType(MonsterStateData* pThis, uint8_t moveType);
    uint8_t MonsterStateData_GetEmotionState(MonsterStateData* pThis);
    void MonsterStateData_SetEmotionState(MonsterStateData* pThis, uint8_t emotionState);
    uint8_t MonsterStateData_GetCombatState(MonsterStateData* pThis);
    void MonsterStateData_SetCombatState(MonsterStateData* pThis, uint8_t combatState);
    bool MonsterStateData_NotEqual(MonsterStateData* pThis, MonsterStateData* pOther);
}
