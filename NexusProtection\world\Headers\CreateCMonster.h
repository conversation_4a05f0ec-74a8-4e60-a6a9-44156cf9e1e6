/*
 * CreateCMonster.h - Monster Creation Handler
 * Refactored for Visual Studio 2022 compatibility
 * Original: CreateCMonsterQEAA_NPEAU_monster_create_setdataZ_140141C50.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <chrono>
#include <array>

// Forward declarations
class CMonster;
class CCharacter;
class CLootingMgr;
class CMonsterAggroMgr;
class CLuaSignalReActor;
class CMonsterSkillPool;
class CMonsterHierarchy;
class EmotionPresentationChecker;
class MonsterSFContDamageToleracne;

// Data structures
struct _monster_create_setdata {
    void* m_pRecordSet;          // _monster_fld* (monster field record)
    float m_fStartPos[3];        // Starting position [x, y, z]
    bool bRobExp;                // Can rob experience
    bool bRewardExp;             // Can reward experience
    bool bDungeon;               // Is dungeon monster
    void* pActiveRec;            // _mon_active* (active record)
    void* pDumPosition;          // _dummy_position* (dummy position)
    void* pParent;               // Parent monster for hierarchy
};

struct _monster_fld {
    float m_fMaxHP;              // Maximum health points
    uint8_t m_bMonsterCondition; // Monster condition (1 = boss, etc.)
    // Additional fields would be defined based on actual structure
};

struct _mon_active {
    void* m_pBlk;                // _mon_block* (monster block)
    // Additional fields would be defined based on actual structure
    
    static void SetCurMonNum(void* pActiveRec, int count);
};

struct _mon_block {
    bool m_bRotate;              // Rotation flag
    // Additional fields would be defined based on actual structure
};

struct _dummy_position {
    float m_fCenterPos[3];       // Center position [x, y, z]
    float m_fDirection[3];       // Direction vector [x, y, z]
    
    static void SetActiveMonNum(void* pDumPosition, int count);
};

/**
 * Monster Creation Handler
 * Handles monster creation, initialization, and setup
 */
class CreateCMonsterHandler {
public:
    // Constructor/Destructor
    CreateCMonsterHandler();
    virtual ~CreateCMonsterHandler();

    // Main monster creation processing
    static bool CreateMonster(CMonster* pMonster, const _monster_create_setdata* pData);
    
    // Validation functions
    static bool ValidateMonsterInstance(const CMonster* pMonster);
    static bool ValidateCreateData(const _monster_create_setdata* pData);
    static bool ValidateMonsterRecord(const _monster_fld* pMonRec);
    
    // Character creation and initialization
    static bool InitializeCharacterBase(CMonster* pMonster, const _monster_create_setdata* pData);
    static void SetupMonsterRecord(CMonster* pMonster, const _monster_create_setdata* pData);
    static void InitializePositions(CMonster* pMonster, const _monster_create_setdata* pData);
    
    // Monster state initialization
    static void InitializeMonsterState(CMonster* pMonster, const _monster_create_setdata* pData);
    static void SetupEmotionSystem(CMonster* pMonster);
    static void InitializeExperienceSettings(CMonster* pMonster, const _monster_create_setdata* pData);
    
    // Active record and position management
    static void ProcessActiveRecord(CMonster* pMonster, const _monster_create_setdata* pData);
    static void ProcessDummyPosition(CMonster* pMonster, const _monster_create_setdata* pData);
    static void SetupRotationSystem(CMonster* pMonster);
    
    // Monster systems initialization
    static void InitializeMonsterSystems(CMonster* pMonster);
    static void InitializeLootingSystem(CMonster* pMonster);
    static void InitializeAggroSystem(CMonster* pMonster);
    static void InitializeDamageSystem(CMonster* pMonster);
    static void InitializeLuaSystem(CMonster* pMonster);
    
    // Monster properties setup
    static void SetupMonsterProperties(CMonster* pMonster);
    static void SetupHealthSystem(CMonster* pMonster);
    static void SetupLifecycleSystem(CMonster* pMonster);
    static void SetupBossSpecialHandling(CMonster* pMonster, const _monster_create_setdata* pData);
    
    // AI and skill systems
    static void InitializeAISystem(CMonster* pMonster);
    static void InitializeSkillSystem(CMonster* pMonster);
    static void SetupMovementSystem(CMonster* pMonster);
    
    // Finalization and messaging
    static void FinalizeMonsterCreation(CMonster* pMonster, const _monster_create_setdata* pData);
    static void SendCreationMessage(CMonster* pMonster);
    static void UpdateHierarchy(CMonster* pMonster, const _monster_create_setdata* pData);
    static void UpdateGlobalCounters();
    
    // Position and rotation utilities
    static void CalculateDirectionVector(const float* centerPos, const float* directionPos, float* result);
    static void NormalizeVector(float* vector);
    static void UpdateLookAtPosition(CMonster* pMonster, const float* targetPos);
    
    // Logging and debugging
    static void LogMonsterCreation(const CMonster* pMonster, const _monster_create_setdata* pData);
    static void LogCreationError(const char* errorMessage, const CMonster* pMonster = nullptr);
    static void LogBossCreation(const CMonster* pMonster);

private:
    // Internal processing helpers
    static void InitializeProcessingContext();
    static void CleanupProcessingContext();
    static bool ValidateCreationParameters(const CMonster* pMonster, const _monster_create_setdata* pData);
    static void ProcessCreationFlow(CMonster* pMonster, const _monster_create_setdata* pData);
    
    // Constants
    static constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    static constexpr size_t STACK_INIT_SIZE = 32 * sizeof(uint32_t);
    static constexpr int DEFAULT_USER_NODE_COUNT = 16;
    static constexpr int BOSS_USER_NODE_COUNT = 64;
    static constexpr int BOSS_CONDITION_VALUE = 1;
    static constexpr float POSITION_COPY_SIZE = 12.0f; // 0xC bytes = 12 bytes = 3 floats
    static constexpr float LOOKAT_OFFSET = -10.0f;
    static constexpr uint32_t DESTROY_TIME_INFINITE = 0xFFFFFFFF; // -1 as unsigned
    static constexpr uint32_t BASE_LIFE_TIME = 600000; // 10 minutes in milliseconds
    static constexpr uint32_t LIFE_TIME_VARIANCE = 60000; // 1 minute variance
    static constexpr int LIFE_TIME_RANDOM_RANGE = 3;
    
    // Disable copy constructor and assignment operator
    CreateCMonsterHandler(const CreateCMonsterHandler&) = delete;
    CreateCMonsterHandler& operator=(const CreateCMonsterHandler&) = delete;
};

/**
 * Legacy C-style interface for compatibility
 * These functions maintain the original calling convention for existing code
 */
namespace CreateCMonsterLegacy {
    // Original function signature for compatibility
    char Create(CMonster* pThis, _monster_create_setdata* pData, float a3);
    
    // Helper functions for legacy integration
    void InitializeLegacySupport();
    void CleanupLegacySupport();
}

/**
 * Utility functions for monster creation management
 */
namespace CreateCMonsterUtils {
    // Parameter validation
    bool IsValidMonster(const CMonster* pMonster);
    bool IsValidCreateData(const _monster_create_setdata* pData);
    bool IsValidPosition(const float* position);
    
    // String utilities
    std::string SafeStringCopy(const char* source, size_t maxLength = 256);
    std::string FormatMonsterInfo(const CMonster* pMonster);
    std::string FormatCreateData(const _monster_create_setdata* pData);
    
    // Position utilities
    void CopyPosition(float* dest, const float* src);
    float CalculateDistance(const float* pos1, const float* pos2);
    void CalculateDirection(const float* from, const float* to, float* direction);
    
    // Time utilities
    uint32_t GetCurrentLoopTime();
    uint8_t GetCurrentMonth();
    uint8_t GetCurrentDay();
    uint8_t GetCurrentHour();
    uint8_t GetCurrentMinute();
    
    // Random utilities
    int GetRandomValue(int range);
    float GetRandomFloat(float min, float max);
    
    // Logging utilities
    void LogCreateCall(const char* functionName, const CMonster* pMonster, const char* details = nullptr);
    void LogError(const char* errorMessage, const char* context = nullptr);
    void LogMonsterOperation(const char* operation, const CMonster* pMonster, bool success);
    void LogSystemInitialization(const char* system, const CMonster* pMonster, bool success);
}

/**
 * Monster creation context structure
 */
struct MonsterCreationContext {
    const CMonster* monster;
    const _monster_create_setdata* createData;
    std::chrono::system_clock::time_point creationTime;
    bool characterInitialized;
    bool systemsInitialized;
    bool aiInitialized;
    std::string creationLog;
    
    MonsterCreationContext() : monster(nullptr), createData(nullptr),
                              creationTime(std::chrono::system_clock::now()),
                              characterInitialized(false), systemsInitialized(false),
                              aiInitialized(false) {}
};

/**
 * Monster creation result enumeration
 */
enum class MonsterCreationResult {
    SUCCESS = 0,
    INVALID_MONSTER = -1,
    INVALID_CREATE_DATA = -2,
    CHARACTER_INIT_FAILED = -3,
    SYSTEMS_INIT_FAILED = -4,
    AI_INIT_FAILED = -5,
    UNKNOWN_ERROR = -6
};

// Type definitions for better code clarity
using MonsterCreateHandler = bool(*)(CMonster*, const _monster_create_setdata*);
using MonsterInitCallback = void(*)(CMonster*);
using ErrorCallback = void(*)(const char*, const CMonster*);

// Constants for monster creation processing
namespace CreateCMonsterConstants {
    constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    constexpr size_t STACK_INIT_SIZE = 32 * sizeof(uint32_t);
    constexpr int DEFAULT_USER_NODE_COUNT = 16;
    constexpr int BOSS_USER_NODE_COUNT = 64;
    constexpr int BOSS_CONDITION_VALUE = 1;
    constexpr float POSITION_COPY_SIZE = 12.0f; // 0xC bytes = 12 bytes = 3 floats
    constexpr float LOOKAT_OFFSET = -10.0f;
    constexpr uint32_t DESTROY_TIME_INFINITE = 0xFFFFFFFF; // -1 as unsigned
    constexpr uint32_t BASE_LIFE_TIME = 600000; // 10 minutes in milliseconds
    constexpr uint32_t LIFE_TIME_VARIANCE = 60000; // 1 minute variance
    constexpr int LIFE_TIME_RANDOM_RANGE = 3;
    
    // Monster states
    constexpr bool MONSTER_OPERATIONAL = true;
    constexpr bool MONSTER_NON_OPERATIONAL = false;
    constexpr bool STANDARD_ITEM_LOOT = true;
    constexpr bool NO_APPARITION = false;
    constexpr int NO_EVENT_ITEMS = 0;
    
    // Movement types
    constexpr int DEFAULT_MOVE_TYPE = 0;
    
    // AI types
    constexpr int DEFAULT_AI_TYPE = 0;
}
