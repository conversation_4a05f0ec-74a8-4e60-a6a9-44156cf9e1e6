/*
 * EnterWorldResult.h - Enter World Result Handler
 * Refactored for Visual Studio 2022 compatibility
 * Original: EnterWorldResultCNetworkEXAEAA_NKPEADZ_1401C06D0.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>

// Forward declarations
class CNetworkEX;
class CMainThread;

// Client ID structure (based on original _CLID)
struct ClientID {
    uint16_t wIndex;      // Client index
    uint32_t dwSerial;    // Client serial number
    
    // Constructor
    ClientID() : wIndex(0), dwSerial(0) {}
    ClientID(uint16_t index, uint32_t serial) : wIndex(index), dwSerial(serial) {}
    
    // Validation
    bool IsValid() const {
        return wIndex > 0 && wIndex <= MAX_CLIENT_INDEX;
    }
    
    // Constants
    static constexpr uint16_t MAX_CLIENT_INDEX = 2532;
};

// External dependencies (these would need to be properly defined)
extern CMainThread g_Main;

/**
 * Enter World Result Handler
 * Handles enter world result messages from the network layer
 */
class EnterWorldResultHandler {
public:
    // Constructor/Destructor
    EnterWorldResultHandler();
    virtual ~EnterWorldResultHandler();

    // Main result processing
    static bool ProcessEnterWorldResult(CNetworkEX* pNetwork, uint32_t messageSize, char* pMessage);
    
    // Validation functions
    static bool ValidateResultMessage(uint32_t messageSize, const char* pMessage);
    static bool ValidateClientID(const ClientID* pClientID);
    static bool ValidateNetworkInstance(const CNetworkEX* pNetwork);
    
    // Message parsing
    static bool ParseClientIDFromMessage(const char* pMessage, uint32_t messageSize, ClientID& clientID);
    static bool ExtractResultCode(const char* pMessage, uint32_t messageSize, uint8_t& resultCode);
    
    // Processing functions
    static bool ForwardToMainThread(uint8_t resultCode, const ClientID* pClientID);
    static bool ProcessValidResult(const ClientID* pClientID);
    static bool ProcessInvalidResult(const ClientID* pClientID);
    
    // Error handling and logging
    static void HandleResultError(const char* errorMessage, const ClientID* pClientID = nullptr);
    static void LogResultActivity(uint32_t messageSize, const ClientID* pClientID, bool success);

private:
    // Internal processing helpers
    static bool ValidateClientIndex(uint16_t clientIndex);
    static void InitializeProcessingContext();
    static void CleanupProcessingContext();
    
    // Constants
    static constexpr uint32_t MAX_MESSAGE_SIZE = 4096;
    static constexpr uint32_t MIN_MESSAGE_SIZE = sizeof(ClientID);
    static constexpr uint16_t MAX_CLIENT_INDEX = 2532;
    
    // Disable copy constructor and assignment operator
    EnterWorldResultHandler(const EnterWorldResultHandler&) = delete;
    EnterWorldResultHandler& operator=(const EnterWorldResultHandler&) = delete;
};

/**
 * Legacy C-style interface for compatibility
 * These functions maintain the original calling convention for existing code
 */
namespace EnterWorldResultLegacy {
    // Original function signature for compatibility
    bool EnterWorldResult(CNetworkEX* pThis, uint32_t messageSize, char* pMessage);
    
    // Helper functions for legacy integration
    void InitializeLegacySupport();
    void CleanupLegacySupport();
}

/**
 * Utility functions for enter world result management
 */
namespace EnterWorldResultUtils {
    // Client ID utilities
    bool IsValidClientIndex(uint16_t clientIndex);
    ClientID CreateClientID(uint16_t index, uint32_t serial);
    std::string ClientIDToString(const ClientID& clientID);
    
    // Message processing utilities
    std::unique_ptr<char[]> CreateSafeMessageCopy(const char* pMessage, uint32_t size);
    bool VerifyMessageIntegrity(const char* pMessage, uint32_t size);
    
    // Logging and debugging
    void LogResultCall(const char* functionName, uint32_t messageSize, const ClientID& clientID, bool success);
    void LogError(const char* errorMessage, const char* context = nullptr);
    void LogClientIDInfo(const ClientID& clientID, const char* action);
}

// Type definitions for better code clarity
using ResultMessageHandler = bool(*)(uint32_t, char*);
using ResultErrorCallback = void(*)(const char*, const ClientID*);

// Constants for result processing
namespace EnterWorldResultConstants {
    constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    constexpr size_t STACK_INIT_SIZE = 12 * sizeof(uint64_t);
    constexpr uint16_t INVALID_CLIENT_INDEX = 0;
    constexpr uint32_t INVALID_CLIENT_SERIAL = 0;
    
    // Result codes
    constexpr uint8_t RESULT_SUCCESS = 1;
    constexpr uint8_t RESULT_FAILURE = 0;
}
