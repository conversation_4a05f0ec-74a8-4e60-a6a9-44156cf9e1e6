# CMoveMapLimitInfo

## Overview

The `CMoveMapLimitInfo` class is an abstract base class that defines the interface for different types of map movement limitations within the NexusProtection game system. It provides a common foundation for implementing various movement restriction mechanisms.

## Purpose

This abstract base class serves as the foundation for:
- **Movement Limitation Types**: Defining different types of movement restrictions (Portal, Zone, Map)
- **Common Interface**: Providing a unified interface for all limitation types
- **Polymorphic Behavior**: Enabling runtime polymorphism for different limitation implementations
- **Store NPC Integration**: Managing associations with store NPCs for purchase-based limitations

## Architecture

### Abstract Base Class Design
The class uses virtual methods to define the interface that derived classes must implement, particularly the `Request` method which handles the core limitation logic.

### Key Components
- **LimitType Enum**: Defines the types of limitations (Portal, Zone, Map)
- **Factory Pattern**: Static `Create` method for instantiating appropriate derived classes
- **Store Integration**: Support for store NPC associations for purchase-based limitations

## Class Interface

### Core Methods
- `Create(uiInx, iType)`: Static factory method for creating limitation objects
- `Request(...)`: Pure virtual method for processing limitation requests (implemented by derived classes)
- `IsEqualLimit(...)`: Compares limitation criteria for matching
- `GetType()`, `GetInx()`, `GetMapIndex()`: Information retrieval methods

### Store Integration
- `GetStoreNPC()`: Returns associated store NPC
- `SetStoreNPC(pStoreNPC)`: Associates a store NPC with the limitation

## Usage Example

```cpp
// Create a limitation info object using the factory method
CMoveMapLimitInfo* pLimitInfo = CMoveMapLimitInfo::Create(portalIndex, 
                                                         static_cast<int>(LimitType::Portal));

if (pLimitInfo)
{
    // Set map index if needed
    pLimitInfo->SetMapIndex(mapIndex);
    
    // Associate with a store NPC if this is a purchase-based limitation
    pLimitInfo->SetStoreNPC(pStoreNPC);
    
    // Check if this limitation matches specific criteria
    if (pLimitInfo->IsEqualLimit(limitType, mapIndex, storeRecordIndex))
    {
        // Process a movement request (implemented by derived class)
        char result = pLimitInfo->Request(requestType, mapIndex, storeRecordIndex,
                                         userIndex, pRequestData, pUserRights);
    }
    
    // Cleanup
    delete pLimitInfo;
}
```

## Limitation Types

### LimitType Enum Values
- **Portal**: Portal-based movement limitations
- **Zone**: Zone-based movement restrictions
- **Map**: Map-level access controls
- **Invalid**: Invalid or unrecognized limitation type

## Dependencies

### Headers Required
- `CMoveMapLimitInfoPortal.h` (for factory method)
- `CMoveMapLimitRightInfo.h` (for rights checking)
- `CItemStore.h` (for store NPC integration)

### Related Classes
- **CMoveMapLimitInfoPortal**: Derived class for portal-specific limitations
- **CMoveMapLimitRightInfo**: User rights and permissions
- **CItemStore**: Store NPC for purchase-based limitations
- **CMoveMapLimitInfoList**: Container for managing multiple limitation objects

## Implementation Notes

### Virtual Interface
- The `Request` method is pure virtual and must be implemented by derived classes
- Virtual destructor ensures proper cleanup of derived classes
- Protected members allow derived classes access to common data

### Factory Pattern
- The static `Create` method instantiates the appropriate derived class based on limitation type
- Currently creates `CMoveMapLimitInfoPortal` for all types (can be extended)
- Handles memory allocation failures gracefully

### Error Handling
- Factory method returns nullptr on allocation failures
- Exception-safe operations with proper cleanup
- Graceful handling of invalid limitation types

## Refactoring Notes

This class was refactored from decompiled C source files to modern C++17/C++20 standards:

### Original Files Consolidated
- `0CMoveMapLimitInfoQEAAIHZ_1403A3D00.c` (Constructor)
- `1CMoveMapLimitInfoQEAAXZ_1403A3D60.c` (Destructor)
- `CreateCMoveMapLimitInfoSAPEAV1IHZ_1403A3DB0.c` (Factory method)
- `IsEqualLimitCMoveMapLimitInfoQEAA_NHHKZ_1403A3E70.c` (Comparison method)
- `GetTypeCMoveMapLimitInfoQEAAHXZ_1403A6F50.c` (Type getter)
- `GetInxCMoveMapLimitInfoQEAAIXZ_1403A74A0.c` (Index getter)

### Modernization Changes
- Converted from C-style to modern C++ abstract base class design
- Added proper virtual interface with pure virtual methods
- Implemented factory pattern for object creation
- Added comprehensive error handling and exception safety
- Used modern C++ enum class for type safety
- Added const-correctness and proper method signatures

## Testing Recommendations

1. **Unit Tests**: Test factory method, getters, and comparison logic
2. **Polymorphism Tests**: Verify virtual method dispatch works correctly
3. **Memory Tests**: Validate proper memory management and no leaks
4. **Integration Tests**: Test interaction with derived classes and store NPCs
5. **Error Tests**: Verify graceful handling of invalid inputs and allocation failures

## Future Enhancements

- Add more derived classes for Zone and Map limitation types
- Implement caching mechanisms for frequently accessed limitations
- Add serialization support for persistent storage
- Consider adding validation methods for limitation criteria
- Implement logging and debugging support

## Related Documentation

- [CMoveMapLimitInfoPortal.md](CMoveMapLimitInfoPortal.md) - Portal-specific implementation
- [CMoveMapLimitInfoList.md](CMoveMapLimitInfoList.md) - Container class
- [CMoveMapLimitManager.md](CMoveMapLimitManager.md) - Main manager class
- [CMoveMapLimitRightInfo.md](CMoveMapLimitRightInfo.md) - Rights management
