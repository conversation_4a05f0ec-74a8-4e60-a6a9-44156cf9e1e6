#pragma once

#include <windows.h>
#include <string>
#include <memory>

// Forward declarations
class CMonster;
class CMapData;
struct _monster_fld;
struct _FILETIME;

namespace NexusProtection {
namespace World {

/**
 * @brief Structure representing an individual monster in an event set
 */
struct EventMonster {
    CMonster* monster = nullptr;
    DWORD serialNumber = 0;
    _monster_fld* monsterField = nullptr;
    
    EventMonster() = default;
    ~EventMonster() = default;
    
    // Disable copy constructor and assignment operator
    EventMonster(const EventMonster&) = delete;
    EventMonster& operator=(const EventMonster&) = delete;
    
    // Enable move constructor and assignment operator
    EventMonster(EventMonster&&) = default;
    EventMonster& operator=(EventMonster&&) = default;
};

/**
 * @brief Structure representing the state of a monster set
 */
struct MonsterSetState {
    bool isOperational = false;
    DWORD startTime = 0;
    DWORD lastUpdateTime = 0;
    int respawnCount = 0;
    static constexpr int MAX_MONSTERS = 25;
    EventMonster monsters[MAX_MONSTERS];
    
    MonsterSetState() = default;
    ~MonsterSetState() = default;
    
    void Initialize();
    void Reset();
};

/**
 * @brief Structure representing a monster set within an event set
 */
struct MonsterSet {
    bool isSet = false;
    DWORD duration = 0;
    DWORD regenTerm = 0;
    BYTE regenProbability = 0;
    float position[3] = {0.0f, 0.0f, 0.0f};
    CMapData* mapData = nullptr;
    _monster_fld* monsterField = nullptr;
    MonsterSetState state;
    
    MonsterSet() = default;
    ~MonsterSet() = default;
    
    // Disable copy constructor and assignment operator
    MonsterSet(const MonsterSet&) = delete;
    MonsterSet& operator=(const MonsterSet&) = delete;
    
    // Enable move constructor and assignment operator
    MonsterSet(MonsterSet&&) = default;
    MonsterSet& operator=(MonsterSet&&) = default;
};

/**
 * @brief Structure representing an event set
 */
struct EventSet {
    char id[64] = {0};
    bool isOperational = false;
    static constexpr int MAX_MONSTER_SETS = 10;
    MonsterSet monsterSets[MAX_MONSTER_SETS];
    
    EventSet() = default;
    ~EventSet() = default;
    
    // Disable copy constructor and assignment operator
    EventSet(const EventSet&) = delete;
    EventSet& operator=(const EventSet&) = delete;
    
    // Enable move constructor and assignment operator
    EventSet(EventSet&&) = default;
    EventSet& operator=(EventSet&&) = default;
};

/**
 * @brief Monster Event Set Manager
 *
 * This class manages monster event sets, handling respawning, loading configurations,
 * and managing the lifecycle of event-based monsters in the game world.
 */
class CMonsterEventSet {
public:
    static constexpr int MAX_EVENT_SETS = 10;

    /**
     * @brief Constructor
     */
    CMonsterEventSet();

    /**
     * @brief Destructor
     */
    ~CMonsterEventSet();

    // Disable copy constructor and assignment operator
    CMonsterEventSet(const CMonsterEventSet&) = delete;
    CMonsterEventSet& operator=(const CMonsterEventSet&) = delete;

    // Enable move constructor and assignment operator
    CMonsterEventSet(CMonsterEventSet&&) = default;
    CMonsterEventSet& operator=(CMonsterEventSet&&) = default;

    /**
     * @brief Check and process event set respawning
     */
    void CheckEventSetRespawn();

    /**
     * @brief Get an empty event set slot
     * @return Pointer to empty event set, or nullptr if none available
     */
    EventSet* GetEmptyEventSet();

    /**
     * @brief Get an event set for looting
     * @return Pointer to looting event set, or nullptr if none available
     */
    EventSet* GetEventSetLooting();

    /**
     * @brief Get a monster set from the event sets
     * @return Pointer to monster set, or nullptr if none found
     */
    MonsterSet* GetMonsterSet();

    /**
     * @brief Load event set configuration from file
     * @param filename Path to the configuration file
     * @return true if successful, false otherwise
     */
    bool LoadEventSet(const char* filename);

    /**
     * @brief Load event set looting configuration
     * @return true if successful, false otherwise
     */
    bool LoadEventSetLooting();

    /**
     * @brief Stop an event set
     * @param filename Configuration filename
     * @param unknown Unknown parameter
     * @return true if successful, false otherwise
     */
    bool StopEventSet(const char* filename, int unknown);

    /**
     * @brief Check if INI file has been modified
     * @param filename Path to the INI file
     * @param lastWriteTime Last known write time
     * @return true if file has been modified, false otherwise
     */
    bool IsINIFileChanged(const char* filename, const _FILETIME& lastWriteTime);

private:
    EventSet m_eventSets[MAX_EVENT_SETS];
    _FILETIME m_lootingWriteTime;

    /**
     * @brief Initialize all event sets
     */
    void InitializeEventSets();

    /**
     * @brief Clean up all event sets
     */
    void CleanupEventSets();

    /**
     * @brief Process monster respawning for a monster set
     * @param eventSet The event set containing the monster set
     * @param monsterSet The monster set to process
     * @param currentTime Current system time
     */
    void ProcessMonsterRespawn(EventSet& eventSet, MonsterSet& monsterSet, DWORD currentTime);
};

} // namespace World
} // namespace NexusProtection
