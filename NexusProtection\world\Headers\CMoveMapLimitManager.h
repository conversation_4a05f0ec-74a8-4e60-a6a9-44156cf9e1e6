#pragma once

/**
 * @file CMoveMapLimitManager.h
 * @brief Map movement limitation and rights management system
 * @details Singleton class that manages map movement restrictions and user permissions
 * <AUTHOR> Development Team
 * @date 2024
 */

#include <vector>
#include <memory>

// Forward declarations
class CMoveMapLimitInfoList;
class CMoveMapLimitRightInfoList;
class CMoveMapLimitRightInfo;
class CPlayer;

/**
 * @class CMoveMapLimitManager
 * @brief Singleton manager for map movement limitations and user rights
 * 
 * This class manages the map movement limitation system, including:
 * - User movement rights and permissions
 * - Map access restrictions
 * - Portal and zone limitations
 * - Player-specific movement controls
 */
class CMoveMapLimitManager
{
public:
    // Singleton pattern methods
    static CMoveMapLimitManager* Instance();
    static void Destroy();

    // Core functionality
    bool Init();
    void Load(CPlayer* pkPlayer);
    char Request(int iLimitType, int iRequestType, int iMapIndex, 
                unsigned int dwStoreRecordIndex, int iUserIndex, char* pRequest);

    // Destructor
    ~CMoveMapLimitManager();

private:
    // Private constructor for singleton pattern
    CMoveMapLimitManager();
    
    // Prevent copying
    CMoveMapLimitManager(const CMoveMapLimitManager&) = delete;
    CMoveMapLimitManager& operator=(const CMoveMapLimitManager&) = delete;

private:
    // Static singleton instance
    static CMoveMapLimitManager* ms_Instance;

    // Member variables
    CMoveMapLimitRightInfoList m_kRightInfo;    ///< User rights information list
    CMoveMapLimitInfoList m_kLimitInfo;         ///< Map limitation information list
};
