#pragma once

/**
 * @file CMoveMapLimitInfoPortal.h
 * @brief Portal-specific map movement limitation information
 * @details Derived class that implements portal-specific movement limitations
 * <AUTHOR> Development Team
 * @date 2024
 */

#include "CMoveMapLimitInfo.h"
#include <vector>
#include <string>
#include <memory>

// Forward declarations
class CMyTimer;
class CDummy;
class CMoveMapLimitRightInfo;

/**
 * @enum NotifyForceMoveHQState
 * @brief States for force move headquarters notification
 */
enum class NotifyForceMoveHQState : int
{
    Idle = 0,           ///< No notification in progress
    Processing = 1,     ///< Notification being processed
    Completed = 2       ///< Notification completed
};

/**
 * @class CMoveMapLimitInfoPortal
 * @brief Portal-specific implementation of map movement limitations
 * 
 * This class extends CMoveMapLimitInfo to provide portal-specific functionality for:
 * - Portal-based movement restrictions
 * - Dummy object management (source, destination, regeneration)
 * - Allowed dummy code validation
 * - Force move headquarters notifications
 * - Timer-based processing for notifications
 */
class CMoveMapLimitInfoPortal : public CMoveMapLimitInfo
{
public:
    // Constructor and destructor
    CMoveMapLimitInfoPortal(unsigned int uiInx, int iType);
    virtual ~CMoveMapLimitInfoPortal();

    // Override virtual methods from base class
    virtual char Request(int iRequestType, int iMapIndex, unsigned int dwStoreRecordIndex,
                        int iUserIndex, char* pRequest, CMoveMapLimitRightInfo* pkRight) override;

    // Portal-specific functionality
    bool LoadINI();
    
    // Dummy management
    void SetSrcDummy(CDummy* pDummy);
    void SetDestDummy(CDummy* pDummy);
    void SetRegenDummy(CDummy* pDummy);
    
    CDummy* GetSrcDummy() const;
    CDummy* GetDestDummy() const;
    CDummy* GetRegenDummy() const;

    // Allowed dummy code management
    void AddAllowedDummyCode(const std::string& code);
    void ClearAllowedDummyCodes();
    bool IsAllowedDummyCode(const std::string& code) const;
    size_t GetAllowedDummyCodeCount() const;

    // Force move HQ notification
    void SetNotifyForceMoveHQState(NotifyForceMoveHQState state);
    NotifyForceMoveHQState GetNotifyForceMoveHQState() const;
    
    void SetNotifyForceMoveHQTimer(CMyTimer* pTimer);
    CMyTimer* GetNotifyForceMoveHQTimer() const;

    // Processing notification index
    void SetProcNotifyInx(unsigned int uiInx);
    unsigned int GetProcNotifyInx() const;

protected:
    // Request processing methods
    char ProcForceMoveHQ(char* pRequest);
    char ProcGotoLimitZone(char* pRequest);
    char ProcUseMoveScroll(char* pRequest);
    
    // Sub-processing methods
    void SubProcForceMoveHQ();
    char SubProcGotoLimitZone();
    void SubProcNotifyForceMoveHQ();

private:
    // Prevent copying
    CMoveMapLimitInfoPortal(const CMoveMapLimitInfoPortal&) = delete;
    CMoveMapLimitInfoPortal& operator=(const CMoveMapLimitInfoPortal&) = delete;

    // Helper methods
    void InitializeMembers();
    void CleanupTimers();

private:
    // Member variables
    CDummy* m_pkSrcDummy;                           ///< Source dummy object
    CDummy* m_pkDestDummy;                          ///< Destination dummy object
    CDummy* m_pkRegenDummy;                         ///< Regeneration dummy object
    
    std::vector<std::string> m_vecAllowDummyCode;   ///< Vector of allowed dummy codes
    
    NotifyForceMoveHQState m_eNotifyForceMoveHQState;   ///< State of force move HQ notification
    CMyTimer* m_pkNotifyForceMoveHQTimer;               ///< Timer for force move HQ notifications
    unsigned int m_uiProcNotifyInx;                     ///< Processing notification index
};
