/*
 * OpenWorldFailureResult.h - World Opening Failure Handler
 * Refactored for Visual Studio 2022 compatibility
 * Original: pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1401F56F0.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <chrono>

// Forward declarations
class CMainThread;
class CWnd;

/**
 * World Opening Failure Result Handler
 * Handles failed world opening operations and error reporting
 */
class OpenWorldFailureResultHandler {
public:
    // Constructor/Destructor
    OpenWorldFailureResultHandler();
    virtual ~OpenWorldFailureResultHandler();

    // Main world opening failure processing
    static void ProcessOpenWorldFailureResult(CMainThread* pMainThread, const char* szMsg);
    
    // Validation functions
    static bool ValidateMainThreadInstance(const CMainThread* pMainThread);
    static bool ValidateErrorMessage(const char* szMsg);
    
    // Error handling and reporting
    static void DisplayFailureMessage(const char* title, const char* message);
    static void LogFailureEvent(const CMainThread* pMainThread, const char* szMsg);
    static void NotifySystemFailure(CWnd* pFrame);
    
    // Message box utilities
    static void ShowErrorDialog(const char* title, const char* message);
    static void ShowCustomMessageBox(const char* title, const char* message, uint32_t type = 0);
    
    // System shutdown and cleanup
    static void InitiateSystemShutdown(CWnd* pFrame);
    static void SendCloseMessage(CWnd* pFrame, uint32_t message);
    static bool IsSystemShuttingDown();
    
    // Logging and history functions
    static void WriteFailureHistory(const char* format, ...);
    static void LogSystemError(CMainThread* pMainThread, const char* errorMessage);
    static void LogWorldOpenFailure(const char* szMsg);
    
    // Error context management
    static void SetFailureContext(const std::string& context);
    static std::string GetFailureContext();
    static void ClearFailureContext();
    
    // Recovery and retry mechanisms
    static bool CanRetryWorldOpen();
    static void SetRetryAttempts(int attempts);
    static int GetRetryAttempts();

private:
    // Internal processing helpers
    static void InitializeProcessingContext();
    static void CleanupProcessingContext();
    static std::string FormatErrorMessage(const char* szMsg);
    static std::string GetCurrentTimestamp();
    
    // Message handling
    static void ProcessFailureMessage(const char* szMsg);
    static void HandleCriticalFailure(const char* szMsg);
    
    // Constants
    static constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    static constexpr size_t STACK_INIT_SIZE = 8 * sizeof(uint32_t);
    static constexpr uint32_t WM_CLOSE_MESSAGE = 0x10;
    static constexpr int MAX_RETRY_ATTEMPTS = 3;
    
    // Static members for failure context
    static std::string s_failureContext;
    static int s_retryAttempts;
    static bool s_systemShuttingDown;
    
    // Disable copy constructor and assignment operator
    OpenWorldFailureResultHandler(const OpenWorldFailureResultHandler&) = delete;
    OpenWorldFailureResultHandler& operator=(const OpenWorldFailureResultHandler&) = delete;
};

/**
 * Legacy C-style interface for compatibility
 * These functions maintain the original calling convention for existing code
 */
namespace OpenWorldFailureResultLegacy {
    // Original function signature for compatibility
    void pc_OpenWorldFailureResult(CMainThread* pThis, char* szMsg);
    
    // Helper functions for legacy integration
    void InitializeLegacySupport();
    void CleanupLegacySupport();
}

/**
 * Utility functions for world opening failure management
 */
namespace OpenWorldFailureResultUtils {
    // Message validation and formatting
    bool IsValidErrorMessage(const char* szMsg);
    std::string SafeStringCopy(const char* source, size_t maxLength = 512);
    std::string FormatFailureMessage(const char* szMsg);
    
    // Time utilities
    std::string GetFormattedTimestamp();
    std::chrono::system_clock::time_point GetCurrentTime();
    
    // System utilities
    bool IsMainThreadValid(const CMainThread* pMainThread);
    std::string MainThreadToString(const CMainThread* pMainThread);
    
    // Logging utilities
    void LogFailureCall(const char* functionName, const CMainThread* pMainThread, const char* szMsg);
    void LogError(const char* errorMessage, const char* context = nullptr);
    void LogSystemShutdown(const char* reason);
    
    // Dialog and UI utilities
    void ShowMessageDialog(const char* title, const char* message);
    bool IsUIAvailable();
    void SafeShowMessage(const char* title, const char* message);
}

/**
 * World opening failure context structure
 */
struct WorldOpenFailureContext {
    std::string errorMessage;
    std::chrono::system_clock::time_point timestamp;
    std::string mainThreadInfo;
    int retryCount;
    bool isCritical;
    
    WorldOpenFailureContext() : timestamp(std::chrono::system_clock::now()), retryCount(0), isCritical(false) {}
    
    WorldOpenFailureContext(const char* msg) 
        : errorMessage(msg ? msg : ""), 
          timestamp(std::chrono::system_clock::now()), 
          retryCount(0), 
          isCritical(false) {}
};

/**
 * Failure severity levels
 */
enum class FailureSeverity {
    INFO = 0,
    WARNING = 1,
    ERROR = 2,
    CRITICAL = 3,
    FATAL = 4
};

// Type definitions for better code clarity
using FailureHandler = void(*)(CMainThread*, const char*);
using MessageBoxHandler = void(*)(const char*, const char*);
using ErrorCallback = void(*)(const char*, const CMainThread*);

// Constants for world opening failure processing
namespace OpenWorldFailureResultConstants {
    constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    constexpr size_t STACK_INIT_SIZE = 8 * sizeof(uint32_t);
    constexpr uint32_t WM_CLOSE_MESSAGE = 0x10;
    constexpr int MAX_RETRY_ATTEMPTS = 3;
    constexpr size_t MAX_ERROR_MESSAGE_LENGTH = 512;
    
    // Default messages
    constexpr const char* DEFAULT_TITLE = "pc_OpenWorldFailureResult";
    constexpr const char* DEFAULT_MESSAGE = "request world-open fail";
    constexpr const char* CRITICAL_FAILURE_TITLE = "Critical World Open Failure";
    constexpr const char* SYSTEM_SHUTDOWN_MESSAGE = "System shutting down due to world open failure";
    
    // Message box types
    constexpr uint32_t MB_OK = 0x00000000L;
    constexpr uint32_t MB_ICONERROR = 0x00000010L;
    constexpr uint32_t MB_ICONWARNING = 0x00000030L;
    constexpr uint32_t MB_ICONINFORMATION = 0x00000040L;
}
