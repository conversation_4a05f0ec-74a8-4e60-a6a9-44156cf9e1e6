/*
 * AlterWorldService.h - World Service Alteration Handler
 * Refactored for Visual Studio 2022 compatibility
 * Original: pc_AlterWorldServiceCMainThreadQEAAX_NZ_1401F61B0.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>

// Forward declarations
class CMainThread;

/**
 * World Service Alteration Handler
 * Handles world service alteration operations in the main thread
 */
class AlterWorldServiceHandler {
public:
    // Constructor/Destructor
    AlterWorldServiceHandler();
    virtual ~AlterWorldServiceHandler();

    // Main service alteration processing
    static void ProcessAlterWorldService(CMainThread* pMainThread, bool bService);
    
    // Validation functions
    static bool ValidateMainThreadInstance(const CMainThread* pMainThread);
    static bool ValidateServiceState(bool bService);
    
    // Service management functions
    static void SetServiceForce(CMainThread* pMainThread, bool bService);
    static bool GetCurrentServiceState(const CMainThread* pMainThread);
    
    // Error handling and logging
    static void HandleServiceError(const char* errorMessage, const CMainThread* pMainThread = nullptr);
    static void LogServiceActivity(const CMainThread* pMainThread, bool bService, const char* operation);

private:
    // Internal processing helpers
    static void InitializeProcessingContext();
    static void CleanupProcessingContext();
    static bool ValidateServiceOperation(const CMainThread* pMainThread, bool bService);
    
    // Constants
    static constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    static constexpr size_t STACK_INIT_SIZE = 8 * sizeof(uint32_t);
    
    // Disable copy constructor and assignment operator
    AlterWorldServiceHandler(const AlterWorldServiceHandler&) = delete;
    AlterWorldServiceHandler& operator=(const AlterWorldServiceHandler&) = delete;
};

/**
 * Legacy C-style interface for compatibility
 * These functions maintain the original calling convention for existing code
 */
namespace AlterWorldServiceLegacy {
    // Original function signature for compatibility
    void pc_AlterWorldService(CMainThread* pThis, bool bService);
    
    // Helper functions for legacy integration
    void InitializeLegacySupport();
    void CleanupLegacySupport();
}

/**
 * Utility functions for world service management
 */
namespace AlterWorldServiceUtils {
    // Service state validation
    bool IsValidServiceState(bool bService);
    
    // Main thread utilities
    bool IsValidMainThread(const CMainThread* pMainThread);
    std::string MainThreadToString(const CMainThread* pMainThread);
    
    // Logging and debugging
    void LogServiceCall(const char* functionName, const CMainThread* pMainThread, bool bService);
    void LogError(const char* errorMessage, const char* context = nullptr);
    void LogServiceStateChange(const CMainThread* pMainThread, bool oldState, bool newState);
}

// Type definitions for better code clarity
using ServiceStateHandler = void(*)(CMainThread*, bool);
using ServiceErrorCallback = void(*)(const char*, const CMainThread*);

// Constants for service processing
namespace AlterWorldServiceConstants {
    constexpr uint32_t STACK_INIT_PATTERN = 0xCCCCCCCC;  // Debug pattern (equivalent to -858993460)
    constexpr size_t STACK_INIT_SIZE = 8 * sizeof(uint32_t);
    constexpr bool DEFAULT_SERVICE_STATE = false;
    
    // Service operation types
    constexpr const char* OPERATION_ENABLE = "ENABLE";
    constexpr const char* OPERATION_DISABLE = "DISABLE";
    constexpr const char* OPERATION_TOGGLE = "TOGGLE";
}
