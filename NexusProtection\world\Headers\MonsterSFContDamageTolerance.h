#pragma once

#include <cstdint>
#include <memory>
#include <string>

// Forward declarations
class CMonster;

/**
 * @class MonsterSFContDamageTolerance
 * @brief Manages special function continuous damage tolerance for monsters
 * 
 * This class handles the tolerance system for special function continuous damage
 * effects on monsters. It tracks tolerance probability, manages recovery over time,
 * and determines whether continuous damage effects should be applied based on
 * current tolerance levels.
 * 
 * Key Features:
 * - Dynamic tolerance probability management
 * - Time-based tolerance recovery
 * - Probabilistic damage resistance calculation
 * - Integration with monster continuous damage systems
 * 
 * @note Refactored from decompiled MonsterSFContDamageToleracne structure
 */
class MonsterSFContDamageTolerance {
public:
    // Constants
    static constexpr float DEFAULT_TOLERANCE = 1.0f;
    static constexpr float MIN_TOLERANCE = 0.0f;
    static constexpr float MAX_TOLERANCE = 1.0f;
    static constexpr float RECOVERY_RATE = 0.02f;
    static constexpr uint32_t RECOVERY_INTERVAL_MS = 1000; // 1 second
    static constexpr uint32_t INVALID_TIME = 0xFFFFFFFF;

    /**
     * @brief Default constructor
     * Initializes tolerance system with default values
     */
    MonsterSFContDamageTolerance();

    /**
     * @brief Copy constructor
     * @param other MonsterSFContDamageTolerance to copy from
     */
    MonsterSFContDamageTolerance(const MonsterSFContDamageTolerance& other);

    /**
     * @brief Destructor
     */
    ~MonsterSFContDamageTolerance();

    /**
     * @brief Initializes the tolerance system with maximum tolerance value
     * @param maxToleranceValue Maximum tolerance probability (0.0 - 1.0)
     */
    void Init(float maxToleranceValue);

    /**
     * @brief One-time initialization with monster reference
     * @param pMonster Pointer to the monster this tolerance system belongs to
     */
    void OnlyOnceInit(CMonster* pMonster);

    /**
     * @brief Main update function called each frame
     * Handles tolerance recovery over time when not under continuous damage
     */
    void Update();

    /**
     * @brief Checks if special function continuous damage should be applied
     * @return true if damage should be applied (tolerance check failed)
     */
    bool IsSFContDamage();

    /**
     * @brief Modifies tolerance probability by a variation amount
     * @param addValue Amount to add to current tolerance (can be negative)
     */
    void SetSFDamageTolerance_Variation(float addValue);

    /**
     * @brief Gets the current tolerance probability
     * @return Current tolerance probability (0.0 - 1.0)
     */
    float GetToleranceProb() const { return m_fToleranceProb; }

    /**
     * @brief Gets the maximum tolerance probability
     * @return Maximum tolerance probability (0.0 - 1.0)
     */
    float GetToleranceProbMax() const { return m_fToleranceProbMax; }

    /**
     * @brief Gets the last update time
     * @return Last update time in milliseconds
     */
    uint32_t GetLastUpdateTime() const { return m_dwLastUpdateTime; }

    /**
     * @brief Gets the associated monster
     * @return Pointer to the associated monster
     */
    CMonster* GetMonster() const { return m_pMonster; }

    /**
     * @brief Sets the tolerance probability directly
     * @param tolerance New tolerance probability (0.0 - 1.0)
     */
    void SetToleranceProb(float tolerance);

    /**
     * @brief Sets the maximum tolerance probability
     * @param maxTolerance New maximum tolerance probability (0.0 - 1.0)
     */
    void SetToleranceProbMax(float maxTolerance);

    /**
     * @brief Resets tolerance to maximum value
     */
    void ResetToleranceToMax();

    /**
     * @brief Validates the current state of the tolerance system
     * @return true if the system is in a valid state
     */
    bool IsValid() const;

    /**
     * @brief Creates a string representation of the tolerance state
     * @return String describing the current tolerance state
     */
    std::string ToString() const;

    /**
     * @brief Assignment operator
     * @param rhs Right-hand side MonsterSFContDamageTolerance
     * @return Reference to this MonsterSFContDamageTolerance
     */
    MonsterSFContDamageTolerance& operator=(const MonsterSFContDamageTolerance& rhs);

protected:
    /**
     * @brief Validates and clamps tolerance values to valid range
     * @param tolerance Tolerance value to validate
     * @return Clamped tolerance value
     */
    float ValidateTolerance(float tolerance) const;

    /**
     * @brief Checks if enough time has passed for tolerance recovery
     * @return true if recovery should occur
     */
    bool ShouldRecover() const;

    /**
     * @brief Performs tolerance recovery calculation
     */
    void ProcessRecovery();

private:
    // Member variables (maintaining original structure layout where possible)
    CMonster* m_pMonster;                   ///< Pointer to the associated monster
    float m_fToleranceProb;                 ///< Current tolerance probability
    uint32_t m_dwLastUpdateTime;            ///< Last update time in milliseconds
    float m_fToleranceProbMax;              ///< Maximum tolerance probability

    // Private helper methods
    void InitializeDefaults();
    void CleanupResources();
    bool IsMonsterValid() const;
    uint32_t GetCurrentTime() const;
    int GenerateRandomPercent() const;

    // Disable default assignment if not explicitly implemented
    // (Already implemented above, but keeping this pattern for consistency)
};

// Utility functions for monster SF continuous damage tolerance management
namespace MonsterSFContDamageToleranceUtils {
    /**
     * @brief Creates a new tolerance system instance
     * @return Unique pointer to the created MonsterSFContDamageTolerance
     */
    std::unique_ptr<MonsterSFContDamageTolerance> CreateTolerance();

    /**
     * @brief Creates a tolerance system with specific settings
     * @param maxTolerance Maximum tolerance probability
     * @param currentTolerance Current tolerance probability
     * @return Unique pointer to the configured MonsterSFContDamageTolerance
     */
    std::unique_ptr<MonsterSFContDamageTolerance> CreateConfiguredTolerance(
        float maxTolerance, 
        float currentTolerance
    );

    /**
     * @brief Validates a tolerance system configuration
     * @param pTolerance Pointer to the MonsterSFContDamageTolerance to validate
     * @return true if the configuration is valid
     */
    bool ValidateTolerance(const MonsterSFContDamageTolerance* pTolerance);

    /**
     * @brief Calculates tolerance effectiveness percentage
     * @param pTolerance Pointer to the MonsterSFContDamageTolerance
     * @return Tolerance effectiveness as percentage (0-100)
     */
    int CalculateTolerancePercentage(const MonsterSFContDamageTolerance* pTolerance);

    /**
     * @brief Gets the memory footprint of a tolerance system
     * @param pTolerance Pointer to the MonsterSFContDamageTolerance
     * @return Size in bytes of the tolerance system's memory usage
     */
    size_t GetMemoryFootprint(const MonsterSFContDamageTolerance* pTolerance);
}

// Legacy C-style interface for compatibility
extern "C" {
    void MonsterSFContDamageTolerance_Constructor(MonsterSFContDamageTolerance* pThis);
    void MonsterSFContDamageTolerance_Destructor(MonsterSFContDamageTolerance* pThis);
    void MonsterSFContDamageTolerance_Init(MonsterSFContDamageTolerance* pThis, float maxToleranceValue);
    void MonsterSFContDamageTolerance_OnlyOnceInit(MonsterSFContDamageTolerance* pThis, CMonster* pMonster);
    void MonsterSFContDamageTolerance_Update(MonsterSFContDamageTolerance* pThis);
    bool MonsterSFContDamageTolerance_IsSFContDamage(MonsterSFContDamageTolerance* pThis);
    void MonsterSFContDamageTolerance_SetSFDamageTolerance_Variation(MonsterSFContDamageTolerance* pThis, float addValue);
    float MonsterSFContDamageTolerance_GetToleranceProb(MonsterSFContDamageTolerance* pThis);
}
