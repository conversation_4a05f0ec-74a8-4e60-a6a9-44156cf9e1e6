# LightMapUtil Module

## Overview

The LightMapUtil module provides comprehensive light mapping functionality for texture operations, color sampling, and rendering support. This module has been refactored from multiple decompiled C source files to create a unified, modern C++17/C++20 light mapping system.

## Original Sources

This module consolidates the following original functions:

- **GetLightMapColor** (Address: `0x140502530`)
- **GetLightMapSurface** (Address: `0x1405025F0`)
- **GetLightMapTexSize** (Address: `0x140500900`)
- **SetLightMap** (Address: `0x1404EDF30`)
- **LoadLightMap** (Address: `0x1405023A0`)
- **ReleaseLightMap** (Address: `0x140502480`)
- **LightMappingTex1** (Address: `0x1404EFAF0`)
- **UnLightMappingTex1** (Address: `0x1404EFB90`)
- **DrawLightMapGroup** (Address: `0x1404F1590`)

## Files

- **Header**: `NexusProtection/world/Headers/LightMapUtil.h`
- **Source**: `NexusProtection/world/Source/LightMapUtil.cpp`
- **Documentation**: `NexusProtection/world/Documents/LightMapUtil_README.md`

## Key Features

### Modern C++ Implementation
- RAII resource management
- Exception safety with strong guarantees
- Move semantics for efficient data transfer
- Type-safe enumerations and constants
- Modern STL algorithms and containers

### Light Map Management
- Automatic resource cleanup
- Texture loading and validation
- UV coordinate clamping and validation
- Color format conversion (RGB565 ↔ ARGB8888)
- Thread-safe operations where applicable

### DirectX Integration
- DirectX 8 texture surface management
- Automatic device state management
- Multi-texture support
- Blending mode control

## Data Structures

### LightMapData
```cpp
struct LightMapData {
    uint16_t width;
    uint16_t height;
    void* pixelData;
    
    // Move-only semantics
    LightMapData(LightMapData&& other) noexcept;
    LightMapData& operator=(LightMapData&& other) noexcept;
};
```

### LightMapTexture
```cpp
struct LightMapTexture {
    uint32_t textureId;
    uint16_t width;
    uint16_t height;
    IDirect3DTexture8* d3dTexture;
};
```

### LightMapColor
```cpp
struct LightMapColor {
    float u, v;  // UV coordinates
    
    void Clamp();           // Clamp to [0.0, 1.0]
    bool IsValid() const;   // Validate range
};
```

## API Reference

### Core Functions

#### GetLightMapColor
```cpp
uint32_t GetLightMapColor(float* uvCoords, int lightMapIndex);
uint32_t GetLightMapColor(const LightMapColor& color, int lightMapIndex);
```
Samples a light map texture at specified UV coordinates and returns ARGB color.

#### GetLightMapSurface
```cpp
IDirect3DTexture8* GetLightMapSurface(int lightMapIndex);
```
Retrieves the DirectX texture surface for a light map.

#### Texture Management
```cpp
uint32_t GetLightMapTexSize();
bool LoadLightMap(const char* filename);
void ReleaseLightMap();
```

#### Rendering Control
```cpp
void SetLightMap(int lightMapIndex);
void EnableLightMappingTex(struct _BSP_MAT_GROUP* materialGroup);
void DisableLightMappingTex();
void DrawLightMapGroup(struct CVertexBuffer* vertexBuffer, 
                       struct _BSP_MAT_GROUP* materialGroup);
```

### Utility Functions

#### Validation
```cpp
bool IsValidLightMapIndex(int lightMapIndex);
bool ValidateLightMapData(int lightMapIndex);
bool IsLightMappingEnabled();
```

#### Color Conversion
```cpp
uint32_t ConvertRGB565ToARGB8888(uint16_t rgb565);
uint16_t ConvertARGB8888ToRGB565(uint32_t argb8888);
```

#### Information Queries
```cpp
int GetLightMapCount();
LightMapTexture GetLightMapInfo(int lightMapIndex);
```

## Usage Examples

### Basic Light Map Color Sampling
```cpp
#include "LightMapUtil.h"

// Sample color at UV coordinates
float uvCoords[2] = {0.5f, 0.5f};
uint32_t color = LightMapUtil::GetLightMapColor(uvCoords, 0);

// Using LightMapColor structure
LightMapColor uv(0.5f, 0.5f);
uint32_t color2 = LightMapUtil::GetLightMapColor(uv, 0);
```

### Loading and Managing Light Maps
```cpp
// Load light map from file
if (LightMapUtil::LoadLightMap("lightmap.r3t")) {
    int count = LightMapUtil::GetLightMapCount();
    std::cout << "Loaded " << count << " light maps" << std::endl;
    
    // Use light maps...
    
    // Clean up when done
    LightMapUtil::ReleaseLightMap();
}
```

### Rendering with Light Maps
```cpp
// Enable light mapping for rendering
LightMapUtil::SetLightMap(0);  // Use light map 0

// Render geometry with light mapping
// ... rendering code ...

// Disable light mapping
LightMapUtil::SetLightMap(-1);
```

### Color Format Conversion
```cpp
// Convert RGB565 to ARGB8888
uint16_t rgb565Color = 0xF800;  // Red in RGB565
uint32_t argbColor = LightMapUtil::ConvertRGB565ToARGB8888(rgb565Color);

// Convert back
uint16_t convertedBack = LightMapUtil::ConvertARGB8888ToRGB565(argbColor);
```

## Legacy Compatibility

The module provides C-style interfaces for compatibility with existing code:

```cpp
extern "C" {
    uint64_t GetLightMapColor(float* uvCoords, int lightMapIndex);
    IDirect3DTexture8* GetLightMapSurface(int lightMapIndex);
    uint64_t GetLightMapTexSize();
    void SetLightMap(int lightMapIndex);
    void LoadLightMap(char* filename);
    void ReleaseLightMap();
    void LightMappingTex1(struct _BSP_MAT_GROUP* materialGroup);
    void UnLightMappingTex1();
    void DrawLightMapGroup(struct CVertexBuffer* vertexBuffer, 
                           struct _BSP_MAT_GROUP* materialGroup);
}
```

## Refactoring Notes

### Changes from Original
1. **Resource Management**: Automatic cleanup with RAII
2. **Type Safety**: Strong typing for coordinates and colors
3. **Error Handling**: Exception safety and validation
4. **Memory Safety**: Bounds checking and null pointer validation
5. **Performance**: Optimized algorithms and reduced allocations

### Preserved Behavior
- Identical UV coordinate clamping behavior
- Same color format conversions
- Compatible DirectX integration
- Preserved original mathematical operations

## Error Handling

The module uses multiple error handling strategies:

1. **Return Values**: Invalid colors return `INVALID_COLOR` constant
2. **Null Checks**: All pointer parameters are validated
3. **Range Validation**: UV coordinates and indices are bounds-checked
4. **Exception Safety**: Strong exception guarantee for resource operations

## Performance Considerations

- UV coordinate clamping is optimized using `std::clamp`
- Color conversions use bit manipulation for efficiency
- Resource allocation is minimized through move semantics
- Validation checks are lightweight and cache-friendly

## Integration Notes

- Compatible with Visual Studio 2022 (v143 toolset)
- Requires C++17 or later for structured bindings and constexpr
- DirectX 8 integration for texture management
- Thread-safe for read operations (write operations require synchronization)

## Future Enhancements

1. **Modern DirectX**: Support for DirectX 11/12
2. **Async Loading**: Asynchronous light map loading
3. **Compression**: Texture compression support
4. **HDR Support**: High dynamic range light maps
5. **GPU Acceleration**: Compute shader-based operations
