/**
 * @file CMoveMapLimitRightInfoList.cpp
 * @brief Implementation of container for managing map movement limitation rights information
 * <AUTHOR> Development Team
 * @date 2024
 */

#include "../Headers/CMoveMapLimitRightInfoList.h"
#include "../Headers/CMoveMapLimitRightInfo.h"
#include <algorithm>
#include <new>

/**
 * @brief Constructor
 * @details Initializes the rights information list container
 */
CMoveMapLimitRightInfoList::CMoveMapLimitRightInfoList()
    : m_vecRight()
{
    InitializeContainer();
}

/**
 * @brief Destructor
 * @details Cleans up all rights information objects and clears the container
 */
CMoveMapLimitRightInfoList::~CMoveMapLimitRightInfoList()
{
    Clear();
}

/**
 * @brief Get rights information by index
 * @param iIndex Index of the rights information to retrieve
 * @return Pointer to the rights information object, or nullptr if index is invalid
 * @details Retrieves the rights information object at the specified index
 */
CMoveMapLimitRightInfo* CMoveMapLimitRightInfoList::Get(int iIndex)
{
    if (!IsValidIndex(iIndex))
    {
        return nullptr;
    }

    try
    {
        return &m_vecRight[static_cast<size_t>(iIndex)];
    }
    catch (...)
    {
        return nullptr;
    }
}

/**
 * @brief Get rights information by index (const version)
 * @param iIndex Index of the rights information to retrieve
 * @return Pointer to the rights information object, or nullptr if index is invalid
 * @details Retrieves the rights information object at the specified index (const version)
 */
const CMoveMapLimitRightInfo* CMoveMapLimitRightInfoList::Get(int iIndex) const
{
    if (!IsValidIndex(iIndex))
    {
        return nullptr;
    }

    try
    {
        return &m_vecRight[static_cast<size_t>(iIndex)];
    }
    catch (...)
    {
        return nullptr;
    }
}

/**
 * @brief Add a rights information object to the list
 * @param rightInfo Rights information object to add
 * @details Adds a copy of the rights information object to the container
 */
void CMoveMapLimitRightInfoList::Add(const CMoveMapLimitRightInfo& rightInfo)
{
    try
    {
        m_vecRight.push_back(rightInfo);
    }
    catch (...)
    {
        // Handle memory allocation failures gracefully
    }
}

/**
 * @brief Clear all rights information objects
 * @details Removes all rights information objects from the container
 */
void CMoveMapLimitRightInfoList::Clear()
{
    try
    {
        m_vecRight.clear();
    }
    catch (...)
    {
        // Handle any exceptions during clearing
    }
}

/**
 * @brief Remove a rights information object by index
 * @param iIndex Index of the rights information to remove
 * @return true if removal successful, false otherwise
 * @details Removes the rights information object at the specified index
 */
bool CMoveMapLimitRightInfoList::Remove(int iIndex)
{
    if (!IsValidIndex(iIndex))
    {
        return false;
    }

    try
    {
        auto it = m_vecRight.begin() + static_cast<size_t>(iIndex);
        m_vecRight.erase(it);
        return true;
    }
    catch (...)
    {
        return false;
    }
}

/**
 * @brief Get the number of rights information objects
 * @return Number of objects in the list
 */
size_t CMoveMapLimitRightInfoList::Size() const
{
    return m_vecRight.size();
}

/**
 * @brief Check if the list is empty
 * @return true if the list is empty, false otherwise
 */
bool CMoveMapLimitRightInfoList::Empty() const
{
    return m_vecRight.empty();
}

/**
 * @brief Check if an index is valid
 * @param iIndex Index to validate
 * @return true if the index is valid, false otherwise
 * @details Validates that the index is within the bounds of the container
 */
bool CMoveMapLimitRightInfoList::IsValidIndex(int iIndex) const
{
    return (iIndex >= 0 && static_cast<size_t>(iIndex) < m_vecRight.size());
}

/**
 * @brief Get iterator to the beginning of the container
 * @return Iterator to the beginning
 */
std::vector<CMoveMapLimitRightInfo>::iterator CMoveMapLimitRightInfoList::begin()
{
    return m_vecRight.begin();
}

/**
 * @brief Get iterator to the end of the container
 * @return Iterator to the end
 */
std::vector<CMoveMapLimitRightInfo>::iterator CMoveMapLimitRightInfoList::end()
{
    return m_vecRight.end();
}

/**
 * @brief Get const iterator to the beginning of the container
 * @return Const iterator to the beginning
 */
std::vector<CMoveMapLimitRightInfo>::const_iterator CMoveMapLimitRightInfoList::begin() const
{
    return m_vecRight.begin();
}

/**
 * @brief Get const iterator to the end of the container
 * @return Const iterator to the end
 */
std::vector<CMoveMapLimitRightInfo>::const_iterator CMoveMapLimitRightInfoList::end() const
{
    return m_vecRight.end();
}

/**
 * @brief Reserve capacity for the container
 * @param capacity Number of elements to reserve space for
 * @details Reserves memory for the specified number of elements to improve performance
 */
void CMoveMapLimitRightInfoList::Reserve(size_t capacity)
{
    try
    {
        m_vecRight.reserve(capacity);
    }
    catch (...)
    {
        // Handle memory allocation failures gracefully
    }
}

/**
 * @brief Get the current capacity of the container
 * @return Current capacity of the container
 */
size_t CMoveMapLimitRightInfoList::Capacity() const
{
    return m_vecRight.capacity();
}

/**
 * @brief Find rights information by right type
 * @param iRightType Type of right to search for
 * @return Pointer to the rights information object, or nullptr if not found
 * @details Searches for the first rights information object with the specified right type
 */
CMoveMapLimitRightInfo* CMoveMapLimitRightInfoList::Find(int iRightType)
{
    try
    {
        for (auto& rightInfo : m_vecRight)
        {
            if (rightInfo.GetType() == iRightType)
            {
                return &rightInfo;
            }
        }
        
        return nullptr;
    }
    catch (...)
    {
        return nullptr;
    }
}

/**
 * @brief Find rights information by right type (const version)
 * @param iRightType Type of right to search for
 * @return Pointer to the rights information object, or nullptr if not found
 * @details Searches for the first rights information object with the specified right type (const version)
 */
const CMoveMapLimitRightInfo* CMoveMapLimitRightInfoList::Find(int iRightType) const
{
    try
    {
        for (const auto& rightInfo : m_vecRight)
        {
            if (rightInfo.GetType() == iRightType)
            {
                return &rightInfo;
            }
        }
        
        return nullptr;
    }
    catch (...)
    {
        return nullptr;
    }
}

/**
 * @brief Initialize the container
 * @details Helper method to initialize the container with default settings
 */
void CMoveMapLimitRightInfoList::InitializeContainer()
{
    try
    {
        // Reserve some initial capacity for efficiency
        m_vecRight.reserve(10);
    }
    catch (...)
    {
        // Handle initialization failures gracefully
    }
}
