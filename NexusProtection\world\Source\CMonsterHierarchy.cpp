#include "../Headers/CMonsterHierarchy.h"
#include <cstring>
#include <cmath>
#include <algorithm>
#include <cassert>
#include <iostream>

// Include necessary headers for dependencies
// These would be replaced with actual headers in the real implementation
class CMonster {
public:
    CMonsterHierarchy m_MonHierarcy;  // Monster hierarchy component
    struct _monster_fld* m_pMonRec;   // Monster record data
    class CMapData* m_pCurMap;        // Current map
    uint16_t m_wMapLayerIndex;        // Map layer index
    float m_fCurPos[3];               // Current position
    bool m_bRobExp;                   // Rob experience flag
    
    // Record set for child monster data
    struct {
        char m_strCode[32];  // Monster code string
    } m_pRecordSet[32];
};

class CMapData {
public:
    static bool GetRandPosVirtualDum(CMapData* pMap, const float* basePos, int range, float* outPos);
};

struct _monster_fld {
    struct {
        int nChildMonNum;           // Number of child monsters
    } m_Child[3];                   // Child data for 3 kinds
    int m_nGuardRecallTimeMS;       // Guard recall time in milliseconds
};

// Global utility functions
extern uint32_t GetLoopTime();
extern CMonster* CreateRepMonster(CMapData* pMap, uint16_t layerIndex, const float* pos, 
                                  const char* monsterCode, CMonster* pParent, 
                                  bool robExp, int param1, int param2, int param3, int param4);

// Global virtual function table
extern CMonsterHierarchyVtbl CMonsterHierarchy_vftable;

// CMonsterHierarchy implementation

CMonsterHierarchy::CMonsterHierarchy()
    : vfptr(nullptr)
    , m_dwTotalCount(0)
    , m_pParentMon(nullptr)
    , m_dwParentSerial(INVALID_SERIAL)
    , m_byChildMonSetNum(0)
    , m_dwChildRecallTime(0)
    , m_pThisMon(nullptr)
{
    InitializeDefaults();
    SetupVirtualFunctionTable();
}

CMonsterHierarchy::~CMonsterHierarchy()
{
    // Maintain virtual function table during destruction
    SetupVirtualFunctionTable();
    CleanupResources();
}

void CMonsterHierarchy::InitializeDefaults()
{
    // Initialize child monster arrays
    for (auto& kindArray : m_pChildMon) {
        kindArray.fill(nullptr);
    }
    
    // Initialize counters
    m_dwMonCount.fill(0);
    
    m_dwTotalCount = 0;
    m_pParentMon = nullptr;
    m_dwParentSerial = INVALID_SERIAL;
    m_byChildMonSetNum = 0;
    m_dwChildRecallTime = 0;
    m_pThisMon = nullptr;
}

void CMonsterHierarchy::CleanupResources()
{
    // Remove all child references (don't delete, just clear references)
    PopChildMonAll();
    
    // Clear parent reference
    m_pParentMon = nullptr;
    m_pThisMon = nullptr;
}

void CMonsterHierarchy::SetupVirtualFunctionTable()
{
    // Equivalent to: this->vfptr = (CMonsterHierarchyVtbl *)&CMonsterHierarchy::`vftable';
    vfptr = &CMonsterHierarchy_vftable;
}

void CMonsterHierarchy::Init()
{
    m_dwTotalCount = 0;
    m_pParentMon = nullptr;
    m_dwParentSerial = INVALID_SERIAL;
    m_byChildMonSetNum = 0;
    
    // Clear all child monster references
    for (int kind = 0; kind < MAX_CHILD_KINDS; ++kind) {
        for (int index = 0; index < MAX_CHILDREN_PER_KIND; ++index) {
            m_pChildMon[kind][index] = nullptr;
        }
        m_dwMonCount[kind] = 0;
    }
    
    m_dwChildRecallTime = 0;
}

void CMonsterHierarchy::OnlyOnceInit(CMonster* pMonster)
{
    Init();
    m_pThisMon = pMonster;
}

void CMonsterHierarchy::OnChildRegenLoop()
{
    if (!m_pThisMon || !m_pThisMon->m_pMonRec) {
        return;
    }
    
    _monster_fld* pMonRec = m_pThisMon->m_pMonRec;
    
    // Only process if we have child kinds configured
    if (ChildKindCount() == 0) {
        return;
    }
    
    // Only parent monsters (not children themselves) can spawn children
    if (!m_pParentMon && GetLoopTime() - m_dwChildRecallTime > static_cast<uint32_t>(pMonRec->m_nGuardRecallTimeMS)) {
        m_dwChildRecallTime = GetLoopTime();
        
        int maxKinds = std::min(static_cast<int>(m_byChildMonSetNum), MAX_CHILD_KINDS);
        
        for (int kind = 0; kind < maxKinds; ++kind) {
            if (pMonRec->m_Child[kind].nChildMonNum <= 0) {
                continue;
            }
            
            // Calculate how many children need to be spawned
            uint32_t neededChildren = pMonRec->m_Child[kind].nChildMonNum - m_dwMonCount[kind];
            
            for (uint32_t i = 0; i < neededChildren; ++i) {
                float newPos[3];
                
                // Get random position near parent
                if (CMapData::GetRandPosVirtualDum(m_pThisMon->m_pCurMap, m_pThisMon->m_fCurPos, 100, newPos)) {
                    // Get monster code from record set
                    const char* monsterCode = m_pThisMon->m_pRecordSet[kind + 26].m_strCode;
                    
                    // Create child monster
                    CMonster* pChild = CreateRepMonster(
                        m_pThisMon->m_pCurMap,
                        m_pThisMon->m_wMapLayerIndex,
                        newPos,
                        monsterCode,
                        m_pThisMon,  // Parent
                        m_pThisMon->m_bRobExp,
                        0, 0, 0, 0
                    );
                    
                    if (pChild) {
                        // Try to add child to hierarchy
                        if (!PushChildMon(kind, pChild)) {
                            // If failed to add, clear parent reference
                            SetParent(nullptr);
                        }
                    }
                }
            }
        }
    }
}

void CMonsterHierarchy::OnChildMonsterCreate(CMonster* pChild, int kind)
{
    if (pChild && ValidateChildParams(kind, 0)) {
        PushChildMon(kind, pChild);
    }
}

CMonster* CMonsterHierarchy::GetParent()
{
    return m_pParentMon;
}

bool CMonsterHierarchy::SetParent(CMonster* pParent)
{
    m_pParentMon = pParent;
    return true;
}

CMonster* CMonsterHierarchy::GetChild(int kind, int index)
{
    if (!ValidateChildParams(kind, index)) {
        return nullptr;
    }
    
    return m_pChildMon[kind][index];
}

bool CMonsterHierarchy::PushChildMon(int kind, CMonster* pChild)
{
    if (!pChild || !ValidateChildParams(kind, 0)) {
        return false;
    }
    
    // Check if child already exists
    if (SearchChildMon(pChild)) {
        return true; // Already exists
    }
    
    // Find empty slot
    int emptySlot = FindEmptyChildSlot(kind);
    if (emptySlot == -1) {
        return false; // No empty slots
    }
    
    // Add child to slot
    AddChildToSlot(kind, emptySlot, pChild);
    
    // Set this monster as parent of the child
    if (pChild) {
        pChild->m_MonHierarcy.SetParent(m_pThisMon);
    }
    
    return true;
}

bool CMonsterHierarchy::PopChildMon(CMonster* pChild)
{
    if (!pChild) {
        return false;
    }
    
    // Search for the child in all kinds
    for (int kind = 0; kind < MAX_CHILD_KINDS; ++kind) {
        for (int index = 0; index < MAX_CHILDREN_PER_KIND; ++index) {
            if (m_pChildMon[kind][index] == pChild) {
                // Clear parent reference in child
                pChild->m_MonHierarcy.SetParent(nullptr);
                
                // Remove from slot
                RemoveChildFromSlot(kind, index);
                return true;
            }
        }
    }
    
    return false;
}

void CMonsterHierarchy::PopChildMonAll()
{
    for (int kind = 0; kind < MAX_CHILD_KINDS; ++kind) {
        for (int index = 0; index < MAX_CHILDREN_PER_KIND; ++index) {
            if (m_pChildMon[kind][index]) {
                // Clear parent reference in child
                m_pChildMon[kind][index]->m_MonHierarcy.SetParent(nullptr);
                
                // Remove from slot
                RemoveChildFromSlot(kind, index);
            }
        }
    }
}

bool CMonsterHierarchy::SearchChildMon(CMonster* pChild)
{
    if (!pChild) {
        return false;
    }
    
    for (int kind = 0; kind < MAX_CHILD_KINDS; ++kind) {
        for (int index = 0; index < MAX_CHILDREN_PER_KIND; ++index) {
            if (m_pChildMon[kind][index] == pChild) {
                return true;
            }
        }
    }
    
    return false;
}

uint32_t CMonsterHierarchy::GetChildCount(int kind) const
{
    if (kind < 0 || kind >= MAX_CHILD_KINDS) {
        return 0;
    }
    
    return m_dwMonCount[kind];
}

uint8_t CMonsterHierarchy::ChildKindCount() const
{
    uint8_t count = 0;
    
    for (int kind = 0; kind < MAX_CHILD_KINDS; ++kind) {
        if (m_dwMonCount[kind] > 0) {
            count++;
        }
    }
    
    return count;
}

bool CMonsterHierarchy::IsValid() const
{
    // Check if total count matches sum of individual counts
    uint32_t calculatedTotal = 0;
    for (const auto& count : m_dwMonCount) {
        calculatedTotal += count;
    }
    
    return calculatedTotal == m_dwTotalCount;
}

int CMonsterHierarchy::SpawnChildrenOfKind(int kind, int count)
{
    if (!ValidateChildParams(kind, 0) || count <= 0) {
        return 0;
    }
    
    int spawned = 0;
    for (int i = 0; i < count; ++i) {
        // Implementation would spawn a child monster
        // This is a placeholder for the actual spawning logic
        spawned++;
    }
    
    return spawned;
}

int CMonsterHierarchy::FindEmptyChildSlot(int kind) const
{
    if (!ValidateChildParams(kind, 0)) {
        return -1;
    }
    
    for (int index = 0; index < MAX_CHILDREN_PER_KIND; ++index) {
        if (m_pChildMon[kind][index] == nullptr) {
            return index;
        }
    }
    
    return -1; // No empty slots
}

bool CMonsterHierarchy::ValidateChildParams(int kind, int index) const
{
    return kind >= 0 && kind < MAX_CHILD_KINDS && 
           index >= 0 && index < MAX_CHILDREN_PER_KIND;
}

void CMonsterHierarchy::UpdateChildCounts()
{
    m_dwTotalCount = 0;
    
    for (int kind = 0; kind < MAX_CHILD_KINDS; ++kind) {
        uint32_t kindCount = 0;
        
        for (int index = 0; index < MAX_CHILDREN_PER_KIND; ++index) {
            if (m_pChildMon[kind][index] != nullptr) {
                kindCount++;
            }
        }
        
        m_dwMonCount[kind] = kindCount;
        m_dwTotalCount += kindCount;
    }
}

void CMonsterHierarchy::CleanupInvalidChildren()
{
    for (int kind = 0; kind < MAX_CHILD_KINDS; ++kind) {
        for (int index = 0; index < MAX_CHILDREN_PER_KIND; ++index) {
            CMonster* pChild = m_pChildMon[kind][index];
            if (pChild) {
                // Check if child is still valid (implementation specific)
                // For now, we assume all children are valid
            }
        }
    }
    
    UpdateChildCounts();
}

bool CMonsterHierarchy::IsChildSlotValid(int kind, int index) const
{
    return ValidateChildParams(kind, index) && m_pChildMon[kind][index] != nullptr;
}

void CMonsterHierarchy::RemoveChildFromSlot(int kind, int index)
{
    if (ValidateChildParams(kind, index) && m_pChildMon[kind][index]) {
        m_pChildMon[kind][index] = nullptr;
        
        if (m_dwMonCount[kind] > 0) {
            m_dwMonCount[kind]--;
        }
        
        if (m_dwTotalCount > 0) {
            m_dwTotalCount--;
        }
    }
}

void CMonsterHierarchy::AddChildToSlot(int kind, int index, CMonster* pChild)
{
    if (ValidateChildParams(kind, index) && pChild && !m_pChildMon[kind][index]) {
        m_pChildMon[kind][index] = pChild;
        m_dwMonCount[kind]++;
        m_dwTotalCount++;
    }
}

// CMonsterHierarchyUtils implementation

namespace CMonsterHierarchyUtils {

std::unique_ptr<CMonsterHierarchy> CreateHierarchy()
{
    return std::make_unique<CMonsterHierarchy>();
}

bool ValidateHierarchy(const CMonsterHierarchy* pHierarchy)
{
    return pHierarchy != nullptr && pHierarchy->IsValid();
}

size_t GetMemoryFootprint(const CMonsterHierarchy* pHierarchy)
{
    if (!pHierarchy) {
        return 0;
    }
    
    // Calculate approximate memory usage
    size_t footprint = sizeof(CMonsterHierarchy);
    
    // Add size of child monster arrays (just pointers, not the monsters themselves)
    footprint += CMonsterHierarchy::MAX_CHILD_KINDS * CMonsterHierarchy::MAX_CHILDREN_PER_KIND * sizeof(CMonster*);
    
    return footprint;
}

int CountHierarchyTree(const CMonsterHierarchy* pHierarchy)
{
    if (!pHierarchy) {
        return 0;
    }
    
    int totalCount = 1; // Count this monster
    
    // Add all children
    for (int kind = 0; kind < CMonsterHierarchy::MAX_CHILD_KINDS; ++kind) {
        for (int index = 0; index < CMonsterHierarchy::MAX_CHILDREN_PER_KIND; ++index) {
            CMonster* pChild = pHierarchy->GetChild(kind, index);
            if (pChild) {
                // Recursively count children of children
                totalCount += CountHierarchyTree(&pChild->m_MonHierarcy);
            }
        }
    }
    
    return totalCount;
}

CMonster* FindRootMonster(const CMonsterHierarchy* pHierarchy)
{
    if (!pHierarchy) {
        return nullptr;
    }
    
    CMonster* pCurrent = pHierarchy->GetThisMonster();
    
    // Traverse up to find root
    while (pCurrent && pCurrent->m_MonHierarcy.GetParent()) {
        pCurrent = pCurrent->m_MonHierarcy.GetParent();
    }
    
    return pCurrent;
}

} // namespace CMonsterHierarchyUtils

// Legacy C-style interface implementation

extern "C" {

void CMonsterHierarchy_Constructor(CMonsterHierarchy* pThis)
{
    if (pThis) {
        new (pThis) CMonsterHierarchy();
    }
}

void CMonsterHierarchy_Destructor(CMonsterHierarchy* pThis)
{
    if (pThis) {
        pThis->~CMonsterHierarchy();
    }
}

void CMonsterHierarchy_Init(CMonsterHierarchy* pThis)
{
    if (pThis) {
        pThis->Init();
    }
}

void CMonsterHierarchy_OnlyOnceInit(CMonsterHierarchy* pThis, CMonster* pMonster)
{
    if (pThis) {
        pThis->OnlyOnceInit(pMonster);
    }
}

void CMonsterHierarchy_OnChildRegenLoop(CMonsterHierarchy* pThis)
{
    if (pThis) {
        pThis->OnChildRegenLoop();
    }
}

CMonster* CMonsterHierarchy_GetParent(CMonsterHierarchy* pThis)
{
    return pThis ? pThis->GetParent() : nullptr;
}

bool CMonsterHierarchy_SetParent(CMonsterHierarchy* pThis, CMonster* pParent)
{
    return pThis ? pThis->SetParent(pParent) : false;
}

CMonster* CMonsterHierarchy_GetChild(CMonsterHierarchy* pThis, int kind, int index)
{
    return pThis ? pThis->GetChild(kind, index) : nullptr;
}

bool CMonsterHierarchy_PushChildMon(CMonsterHierarchy* pThis, int kind, CMonster* pChild)
{
    return pThis ? pThis->PushChildMon(kind, pChild) : false;
}

bool CMonsterHierarchy_PopChildMon(CMonsterHierarchy* pThis, CMonster* pChild)
{
    return pThis ? pThis->PopChildMon(pChild) : false;
}

void CMonsterHierarchy_PopChildMonAll(CMonsterHierarchy* pThis)
{
    if (pThis) {
        pThis->PopChildMonAll();
    }
}

bool CMonsterHierarchy_SearchChildMon(CMonsterHierarchy* pThis, CMonster* pChild)
{
    return pThis ? pThis->SearchChildMon(pChild) : false;
}

uint8_t CMonsterHierarchy_ChildKindCount(CMonsterHierarchy* pThis)
{
    return pThis ? pThis->ChildKindCount() : 0;
}

} // extern "C"
