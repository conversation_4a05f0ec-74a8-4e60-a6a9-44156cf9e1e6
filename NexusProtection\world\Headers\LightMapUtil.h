#pragma once

#include <cstdint>
#include <memory>

// Forward declarations for DirectX types
struct IDirect3DTexture8;
struct IDirect3DDevice8;
struct R3Texture;
struct CVertexBuffer;
struct _BSP_MAT_GROUP;

// Light map data structures
struct LightMapData {
    uint16_t width;
    uint16_t height;
    void* pixelData;
    
    LightMapData() : width(0), height(0), pixelData(nullptr) {}
    ~LightMapData();
    
    // Disable copy constructor and assignment
    LightMapData(const LightMapData&) = delete;
    LightMapData& operator=(const LightMapData&) = delete;
    
    // Enable move constructor and assignment
    LightMapData(LightMapData&& other) noexcept;
    LightMapData& operator=(LightMapData&& other) noexcept;
};

// Light map texture information
struct LightMapTexture {
    uint32_t textureId;
    uint16_t width;
    uint16_t height;
    IDirect3DTexture8* d3dTexture;
    
    LightMapTexture() : textureId(0), width(0), height(0), d3dTexture(nullptr) {}
};

// Color structure for light map operations
struct LightMapColor {
    float u, v;  // UV coordinates
    
    LightMapColor() : u(0.0f), v(0.0f) {}
    LightMapColor(float u_, float v_) : u(u_), v(v_) {}
    
    // Clamp UV coordinates to valid range [0.0, 1.0]
    void Clamp();
    
    // Check if UV coordinates are valid
    bool IsValid() const;
};

/**
 * @namespace LightMapUtil
 * @brief Light mapping utility functions for texture and color operations
 * 
 * This namespace contains utility functions for light map operations including
 * color sampling, texture management, UV coordinate processing, and rendering.
 * 
 * @note Refactored from decompiled light map utility functions
 */
namespace LightMapUtil {

    // Constants
    namespace Constants {
        constexpr float FLOAT_1_0 = 1.0f;
        constexpr float FLOAT_0_0 = 0.0f;
        constexpr uint32_t INVALID_COLOR = 0xFFFFFFFF;
        constexpr int INVALID_TEXTURE_ID = -1;
        constexpr uint32_t DEFAULT_ALPHA = 0xFF000000;
    }

    /**
     * @brief Gets the light map color at specified UV coordinates
     * @param uvCoords Pointer to UV coordinates (modified in-place for clamping)
     * @param lightMapIndex Index of the light map to sample from
     * @return uint32_t The color value in ARGB format, or INVALID_COLOR on error
     * 
     * This function samples a light map texture at the given UV coordinates and
     * returns the color value. UV coordinates are clamped to [0.0, 1.0] range.
     * 
     * @note Original function: GetLightMapColor (Address: 0x140502530)
     */
    uint32_t GetLightMapColor(float* uvCoords, int lightMapIndex);

    /**
     * @brief Gets the light map color using LightMapColor structure
     * @param color The UV coordinates structure
     * @param lightMapIndex Index of the light map to sample from
     * @return uint32_t The color value in ARGB format
     */
    uint32_t GetLightMapColor(const LightMapColor& color, int lightMapIndex);

    /**
     * @brief Gets the DirectX surface for a light map
     * @param lightMapIndex Index of the light map
     * @return IDirect3DTexture8* Pointer to the DirectX texture, or nullptr on error
     * 
     * @note Original function: GetLightMapSurface (Address: 0x1405025F0)
     */
    IDirect3DTexture8* GetLightMapSurface(int lightMapIndex);

    /**
     * @brief Gets the size of light map textures
     * @return uint32_t The texture size (width and height are equal)
     * 
     * @note Original function: GetLightMapTexSize (Address: 0x140500900)
     */
    uint32_t GetLightMapTexSize();

    /**
     * @brief Sets the active light map for rendering
     * @param lightMapIndex Index of the light map to set, or -1 to disable
     * 
     * @note Original function: SetLightMap (Address: 0x1404EDF30)
     */
    void SetLightMap(int lightMapIndex);

    /**
     * @brief Loads light map data from file
     * @param filename Path to the light map file
     * @return bool true if loading was successful
     * 
     * @note Original function: LoadLightMap (Address: 0x1405023A0)
     */
    bool LoadLightMap(const char* filename);

    /**
     * @brief Releases all light map resources
     * 
     * @note Original function: ReleaseLightMap (Address: 0x140502480)
     */
    void ReleaseLightMap();

    /**
     * @brief Enables light mapping for texture rendering
     * @param materialGroup Pointer to the material group
     * 
     * @note Original function: LightMappingTex1 (Address: 0x1404EFAF0)
     */
    void EnableLightMappingTex(struct _BSP_MAT_GROUP* materialGroup);

    /**
     * @brief Disables light mapping for texture rendering
     * 
     * @note Original function: UnLightMappingTex1 (Address: 0x1404EFB90)
     */
    void DisableLightMappingTex();

    /**
     * @brief Draws a light mapped group of vertices
     * @param vertexBuffer Pointer to the vertex buffer
     * @param materialGroup Pointer to the material group
     * 
     * @note Original function: DrawLightMapGroup (Address: 0x1404F1590)
     */
    void DrawLightMapGroup(struct CVertexBuffer* vertexBuffer, 
                           struct _BSP_MAT_GROUP* materialGroup);

    // Utility functions

    /**
     * @brief Validates a light map index
     * @param lightMapIndex The index to validate
     * @return bool true if the index is valid
     */
    bool IsValidLightMapIndex(int lightMapIndex);

    /**
     * @brief Clamps UV coordinates to valid range
     * @param u Reference to U coordinate
     * @param v Reference to V coordinate
     */
    void ClampUVCoordinates(float& u, float& v);

    /**
     * @brief Converts RGB565 to ARGB8888 format
     * @param rgb565 The RGB565 color value
     * @return uint32_t The ARGB8888 color value
     */
    uint32_t ConvertRGB565ToARGB8888(uint16_t rgb565);

    /**
     * @brief Converts ARGB8888 to RGB565 format
     * @param argb8888 The ARGB8888 color value
     * @return uint16_t The RGB565 color value
     */
    uint16_t ConvertARGB8888ToRGB565(uint32_t argb8888);

    /**
     * @brief Gets the number of loaded light maps
     * @return int The number of light maps currently loaded
     */
    int GetLightMapCount();

    /**
     * @brief Checks if light mapping is currently enabled
     * @return bool true if light mapping is enabled
     */
    bool IsLightMappingEnabled();

    /**
     * @brief Gets light map information
     * @param lightMapIndex Index of the light map
     * @return LightMapTexture Information about the light map texture
     */
    LightMapTexture GetLightMapInfo(int lightMapIndex);

    /**
     * @brief Validates light map data integrity
     * @param lightMapIndex Index of the light map to validate
     * @return bool true if the light map data is valid
     */
    bool ValidateLightMapData(int lightMapIndex);

} // namespace LightMapUtil

// Legacy C-style interface for compatibility
extern "C" {
    /**
     * @brief Legacy C interface for GetLightMapColor
     */
    uint64_t GetLightMapColor(float* uvCoords, int lightMapIndex);
    
    /**
     * @brief Legacy C interface for GetLightMapSurface
     */
    IDirect3DTexture8* GetLightMapSurface(int lightMapIndex);
    
    /**
     * @brief Legacy C interface for GetLightMapTexSize
     */
    uint64_t GetLightMapTexSize();
    
    /**
     * @brief Legacy C interface for SetLightMap
     */
    void SetLightMap(int lightMapIndex);
    
    /**
     * @brief Legacy C interface for LoadLightMap
     */
    void LoadLightMap(char* filename);
    
    /**
     * @brief Legacy C interface for ReleaseLightMap
     */
    void ReleaseLightMap();
    
    /**
     * @brief Legacy C interface for LightMappingTex1
     */
    void LightMappingTex1(struct _BSP_MAT_GROUP* materialGroup);
    
    /**
     * @brief Legacy C interface for UnLightMappingTex1
     */
    void UnLightMappingTex1();
    
    /**
     * @brief Legacy C interface for DrawLightMapGroup
     */
    void DrawLightMapGroup(struct CVertexBuffer* vertexBuffer, 
                           struct _BSP_MAT_GROUP* materialGroup);
}
